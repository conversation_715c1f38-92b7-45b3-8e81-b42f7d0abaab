<?php
/**
 * Diagnose Weekly Income Issue
 * Checks why weekly income history is not showing and provides solutions
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/PVSystem.php';

try {
    $db = Database::getInstance();
    $pvSystem = new PVSystem();
    
    echo "🔍 Diagnosing Weekly Income Issue...\n\n";
    
    // Step 1: Check table existence and structure
    echo "1. Checking Table Existence and Structure...\n";
    
    $tables = ['weekly_income_logs', 'weekly_income_reports', 'income_logs', 'pv_transactions'];
    $tableStatus = [];
    
    foreach ($tables as $table) {
        $stmt = $db->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->fetch()) {
            // Get record count
            $countStmt = $db->query("SELECT COUNT(*) as count FROM {$table}");
            $count = $countStmt->fetch()['count'];
            $tableStatus[$table] = $count;
            echo "✅ {$table}: {$count} records\n";
        } else {
            $tableStatus[$table] = 'NOT_EXISTS';
            echo "❌ {$table}: Table does not exist\n";
        }
    }
    echo "\n";
    
    // Step 2: Check if weekly income logs table exists and has data
    echo "2. Checking Weekly Income Data...\n";
    
    if ($tableStatus['weekly_income_logs'] !== 'NOT_EXISTS') {
        if ($tableStatus['weekly_income_logs'] == 0) {
            echo "⚠️ weekly_income_logs table exists but is EMPTY\n";
            echo "   This means no weekly matching has been run yet!\n";
        } else {
            echo "✅ weekly_income_logs has {$tableStatus['weekly_income_logs']} records\n";
            
            // Show recent records
            $recentStmt = $db->query("SELECT user_id, week_start_date, week_end_date, gross_income, net_income FROM weekly_income_logs ORDER BY week_start_date DESC LIMIT 5");
            $recentRecords = $recentStmt->fetchAll();
            
            echo "📋 Recent weekly income records:\n";
            foreach ($recentRecords as $record) {
                echo "   • User {$record['user_id']}: Week {$record['week_start_date']} to {$record['week_end_date']} - ₹{$record['net_income']}\n";
            }
        }
    } else {
        echo "❌ weekly_income_logs table does not exist!\n";
        echo "   You need to run the migration script first.\n";
    }
    echo "\n";
    
    // Step 3: Check if old income_logs has data
    echo "3. Checking Legacy Income Data...\n";
    
    if ($tableStatus['income_logs'] !== 'NOT_EXISTS') {
        if ($tableStatus['income_logs'] == 0) {
            echo "⚠️ income_logs table exists but is EMPTY\n";
        } else {
            echo "✅ income_logs has {$tableStatus['income_logs']} records\n";
            
            // Show recent records
            $recentStmt = $db->query("SELECT user_id, matching_date, income_amount FROM income_logs ORDER BY matching_date DESC LIMIT 5");
            $recentRecords = $recentStmt->fetchAll();
            
            echo "📋 Recent legacy income records:\n";
            foreach ($recentRecords as $record) {
                echo "   • User {$record['user_id']}: {$record['matching_date']} - ₹{$record['income_amount']}\n";
            }
        }
    } else {
        echo "❌ income_logs table does not exist!\n";
    }
    echo "\n";
    
    // Step 4: Check PV transactions (source of income)
    echo "4. Checking PV Transactions (Source Data)...\n";
    
    if ($tableStatus['pv_transactions'] !== 'NOT_EXISTS') {
        if ($tableStatus['pv_transactions'] == 0) {
            echo "❌ pv_transactions table is EMPTY\n";
            echo "   No PV transactions = No income to generate!\n";
        } else {
            echo "✅ pv_transactions has {$tableStatus['pv_transactions']} records\n";
            
            // Check recent PV transactions
            $recentPVStmt = $db->query("SELECT user_id, pv_amount, side, created_at FROM pv_transactions ORDER BY created_at DESC LIMIT 5");
            $recentPV = $recentPVStmt->fetchAll();
            
            echo "📋 Recent PV transactions:\n";
            foreach ($recentPV as $pv) {
                echo "   • User {$pv['user_id']}: {$pv['pv_amount']} PV ({$pv['side']}) on {$pv['created_at']}\n";
            }
            
            // Check PV totals by user
            $pvTotalsStmt = $db->query("
                SELECT user_id, 
                       SUM(CASE WHEN side = 'left' THEN pv_amount ELSE 0 END) as left_pv,
                       SUM(CASE WHEN side = 'right' THEN pv_amount ELSE 0 END) as right_pv
                FROM pv_transactions 
                GROUP BY user_id 
                HAVING left_pv > 0 OR right_pv > 0
                ORDER BY (left_pv + right_pv) DESC 
                LIMIT 5
            ");
            $pvTotals = $pvTotalsStmt->fetchAll();
            
            echo "\n📊 Top users by PV:\n";
            foreach ($pvTotals as $total) {
                $matchable = min($total['left_pv'], $total['right_pv']);
                echo "   • User {$total['user_id']}: Left {$total['left_pv']}, Right {$total['right_pv']}, Matchable: {$matchable}\n";
            }
        }
    }
    echo "\n";
    
    // Step 5: Check active users
    echo "5. Checking Active Users...\n";
    
    $activeUsersStmt = $db->query("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
    $activeUsers = $activeUsersStmt->fetch()['count'];
    echo "👥 Active users: {$activeUsers}\n\n";
    
    // Step 6: Diagnosis and recommendations
    echo str_repeat('=', 60) . "\n";
    echo "🎯 DIAGNOSIS & RECOMMENDATIONS\n";
    echo str_repeat('=', 60) . "\n";
    
    if ($tableStatus['weekly_income_logs'] === 'NOT_EXISTS') {
        echo "❌ ISSUE: Enhanced weekly income tables don't exist\n";
        echo "🔧 SOLUTION: Run migration script\n";
        echo "   Command: php migrate_weekly_income_system.php\n\n";
    } elseif ($tableStatus['weekly_income_logs'] == 0) {
        echo "⚠️ ISSUE: No weekly income has been generated yet\n";
        echo "🔧 SOLUTION: Generate weekly income manually\n";
        echo "   1. Go to Admin Panel → Weekly Income Reports\n";
        echo "   2. Click 'Generate Report'\n";
        echo "   3. Select current week dates\n";
        echo "   4. Click 'Generate Report'\n\n";
        
        if ($tableStatus['pv_transactions'] == 0) {
            echo "⚠️ ADDITIONAL ISSUE: No PV transactions exist\n";
            echo "🔧 ADDITIONAL SOLUTION: Add some PV transactions first\n";
            echo "   1. Go to Admin Panel → Add PV to users\n";
            echo "   2. Or make some product purchases\n";
            echo "   3. Then generate weekly income\n\n";
        }
    } else {
        echo "✅ Weekly income data exists\n";
        echo "🔍 If data still not showing, check:\n";
        echo "   1. User permissions\n";
        echo "   2. Session user ID\n";
        echo "   3. Database query filters\n\n";
    }
    
    // Step 7: Quick fix options
    echo "🚀 QUICK FIX OPTIONS:\n\n";
    
    echo "Option 1: Run Migration (if tables missing)\n";
    echo "   php migrate_weekly_income_system.php\n\n";
    
    echo "Option 2: Create Test Data (if no PV transactions)\n";
    echo "   php create_test_data.php\n\n";
    
    echo "Option 3: Generate Weekly Income (if tables exist but empty)\n";
    echo "   1. Admin Panel → Weekly Income Reports → Generate Report\n";
    echo "   2. Or run: php cron/enhanced-weekly-matching.php\n\n";
    
    echo "Option 4: Use Legacy Data (if old income_logs has data)\n";
    echo "   The system should automatically fall back to income_logs\n\n";
    
    // Step 8: Test current week generation
    if ($tableStatus['pv_transactions'] > 0 && $activeUsers > 0) {
        echo "🧪 TESTING: Can we generate income for current week?\n";
        
        $currentWeekStart = date('Y-m-d', strtotime('monday this week'));
        $currentWeekEnd = date('Y-m-d', strtotime('sunday this week'));
        
        echo "   Test week: {$currentWeekStart} to {$currentWeekEnd}\n";
        
        // Check if already processed
        $checkStmt = $db->prepare("SELECT COUNT(*) as count FROM weekly_income_reports WHERE week_start_date = ?");
        $checkStmt->execute([$currentWeekStart]);
        $alreadyProcessed = $checkStmt->fetch()['count'] > 0;
        
        if ($alreadyProcessed) {
            echo "   ⚠️ Current week already processed\n";
            echo "   Try a different week or clear existing data\n";
        } else {
            echo "   ✅ Current week can be processed\n";
            echo "   Ready to generate weekly income!\n";
        }
    }
    
    echo "\n✅ Diagnosis complete! Follow the recommendations above.\n";
    
} catch (Exception $e) {
    echo "❌ Diagnosis failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
