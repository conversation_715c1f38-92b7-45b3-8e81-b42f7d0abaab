<?php
/**
 * Weekly Income Report Details - Admin Panel
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/PVSystem.php';
require_once '../config/config.php';

// Require admin authentication
Auth::requireAdmin();

$currentUser = Auth::user();
$adminId = Auth::id();

// Get week parameter
$weekStart = $_GET['week'] ?? '';
if (!$weekStart) {
    header("Location: weekly-income-reports.php");
    exit();
}

// Calculate week end date
$weekEnd = date('Y-m-d', strtotime($weekStart . ' +6 days'));

// Get database instance
$db = Database::getInstance();
$config = Config::getInstance();

// Get weekly report summary
$reportStmt = $db->prepare("SELECT * FROM weekly_income_reports WHERE week_start_date = ?");
$reportStmt->execute([$weekStart]);
$report = $reportStmt->fetch();

if (!$report) {
    header("Location: weekly-income-reports.php");
    exit();
}

// Get detailed income logs for this week
$logsStmt = $db->prepare("
    SELECT wil.*, u.full_name, u.email 
    FROM weekly_income_logs wil
    JOIN users u ON wil.user_id = u.user_id
    WHERE wil.week_start_date = ?
    ORDER BY wil.income_amount DESC, u.full_name
");
$logsStmt->execute([$weekStart]);
$incomeLogs = $logsStmt->fetchAll();

// Calculate enhanced statistics
$totalUsers = count($incomeLogs);
$usersWithIncome = count(array_filter($incomeLogs, function($log) {
    return ($log['net_income'] ?? $log['income_amount'] ?? 0) > 0;
}));
$usersWithCapping = count(array_filter($incomeLogs, function($log) {
    return ($log['weekly_capping_applied'] ?? 0) > 0;
}));

// Calculate totals for enhanced display
$totalGrossIncome = array_sum(array_map(function($log) {
    return $log['gross_income'] ?? $log['income_amount'] ?? 0;
}, $incomeLogs));

$totalServiceCharges = array_sum(array_map(function($log) {
    return $log['service_charge'] ?? 0;
}, $incomeLogs));

$totalTdsDeductions = array_sum(array_map(function($log) {
    return $log['tds_deduction'] ?? 0;
}, $incomeLogs));

$totalNetIncome = array_sum(array_map(function($log) {
    return $log['net_income'] ?? $log['income_amount'] ?? 0;
}, $incomeLogs));

$avgNetIncome = $usersWithIncome > 0 ? $totalNetIncome / $usersWithIncome : 0;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weekly Income Details - <?php echo SITE_NAME; ?> Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/navigation.php'; ?>

    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2>
                            <i class="fas fa-chart-line me-2"></i>
                            Weekly Income Details
                        </h2>
                        <p class="text-muted mb-0">
                            <?php echo date('M d', strtotime($weekStart)); ?> - <?php echo date('M d, Y', strtotime($weekEnd)); ?>
                        </p>
                    </div>
                    <a href="weekly-income-reports.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Reports
                    </a>
                </div>
            </div>
        </div>

                <!-- Enhanced Summary Cards with Deduction Breakdown -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h6 class="card-title">Total Users</h6>
                                <h3><?php echo $totalUsers; ?></h3>
                                <small><?php echo $usersWithIncome; ?> earned</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h6 class="card-title">Gross Income</h6>
                                <h4>₹<?php echo number_format($totalGrossIncome, 0); ?></h4>
                                <small>Before deductions</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h6 class="card-title">Service Charge</h6>
                                <h4>₹<?php echo number_format($totalServiceCharges, 0); ?></h4>
                                <small>10% deduction</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-secondary text-white">
                            <div class="card-body text-center">
                                <h6 class="card-title">TDS</h6>
                                <h4>₹<?php echo number_format($totalTdsDeductions, 0); ?></h4>
                                <small>5% deduction</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h6 class="card-title">Net Income</h6>
                                <h4>₹<?php echo number_format($totalNetIncome, 0); ?></h4>
                                <small>Credited to users</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h6 class="card-title">Capping</h6>
                                <h4>₹<?php echo number_format($report['total_capping_applied'] ?? 0, 0); ?></h4>
                                <small><?php echo $usersWithCapping; ?> users</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Deduction Breakdown Chart -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    Income Breakdown & Transparency
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <?php if ($totalGrossIncome > 0): ?>
                                            <div class="mb-3">
                                                <label class="form-label">Gross Income: ₹<?php echo number_format($totalGrossIncome, 2); ?></label>
                                                <div class="progress" style="height: 25px;">
                                                    <div class="progress-bar bg-info" style="width: 100%">
                                                        100%
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Service Charge (10%): ₹<?php echo number_format($totalServiceCharges, 2); ?></label>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-warning" style="width: <?php echo ($totalServiceCharges / $totalGrossIncome) * 100; ?>%">
                                                        <?php echo number_format(($totalServiceCharges / $totalGrossIncome) * 100, 1); ?>%
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">TDS (5%): ₹<?php echo number_format($totalTdsDeductions, 2); ?></label>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-secondary" style="width: <?php echo ($totalTdsDeductions / $totalGrossIncome) * 100; ?>%">
                                                        <?php echo number_format(($totalTdsDeductions / $totalGrossIncome) * 100, 1); ?>%
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">Net Income Credited: ₹<?php echo number_format($totalNetIncome, 2); ?></label>
                                                <div class="progress" style="height: 25px;">
                                                    <div class="progress-bar bg-success" style="width: <?php echo ($totalNetIncome / $totalGrossIncome) * 100; ?>%">
                                                        <?php echo number_format(($totalNetIncome / $totalGrossIncome) * 100, 1); ?>%
                                                    </div>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <div class="text-center py-4">
                                                <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                                                <h5 class="text-muted">No Income Generated</h5>
                                                <p class="text-muted">No matching income was generated for this week.</p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="bg-light p-3 rounded">
                                            <h6 class="fw-bold">Transparency Features:</h6>
                                            <ul class="list-unstyled mb-0">
                                                <li><i class="fas fa-check text-success me-2"></i>All deductions clearly itemized</li>
                                                <li><i class="fas fa-check text-success me-2"></i>Complete audit trail maintained</li>
                                                <li><i class="fas fa-check text-success me-2"></i>Users can view breakdown in dashboard</li>
                                                <li><i class="fas fa-check text-success me-2"></i>PV usage tracking prevents duplicates</li>
                                                <li><i class="fas fa-check text-success me-2"></i>Regulatory compliance ready</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title">Capping Applied</h6>
                                        <h3>₹<?php echo number_format($report['total_capping_applied'], 0); ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Statistics -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">Income Statistics</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">Average Income</small>
                                        <div class="h5">₹<?php echo number_format($avgIncome, 2); ?></div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Users with Capping</small>
                                        <div class="h5"><?php echo $usersWithCapping; ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">Report Status</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <small class="text-muted">Generated</small>
                                        <div class="h6"><?php echo date('M d, Y H:i', strtotime($report['report_generated_at'])); ?></div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">Status</small>
                                        <div>
                                            <?php
                                            $statusClass = [
                                                'generated' => 'bg-warning',
                                                'sent' => 'bg-success',
                                                'failed' => 'bg-danger'
                                            ];
                                            ?>
                                            <span class="badge <?php echo $statusClass[$report['report_status']] ?? 'bg-secondary'; ?>">
                                                <?php echo ucfirst($report['report_status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Income Logs -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">User Income Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>User</th>
                                        <th>PV Details</th>
                                        <th>Gross Income</th>
                                        <th>Service Charge</th>
                                        <th>TDS</th>
                                        <th>Net Income</th>
                                        <th>Capping</th>
                                        <th>Carry Forward</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($incomeLogs as $log): ?>
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($log['full_name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($log['user_id']); ?></small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="small">
                                                    <div><strong>Left:</strong> <span class="badge bg-info"><?php echo number_format($log['left_pv'], 0); ?></span></div>
                                                    <div><strong>Right:</strong> <span class="badge bg-info"><?php echo number_format($log['right_pv'], 0); ?></span></div>
                                                    <div><strong>Matched:</strong> <span class="badge bg-success"><?php echo number_format($log['matched_pv'], 0); ?></span></div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php
                                                $grossIncome = $log['gross_income'] ?? $log['income_amount'] ?? 0;
                                                if ($grossIncome > 0): ?>
                                                    <strong class="text-primary">₹<?php echo number_format($grossIncome, 2); ?></strong>
                                                <?php else: ?>
                                                    <span class="text-muted">₹0.00</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $serviceCharge = $log['service_charge'] ?? 0;
                                                if ($serviceCharge > 0): ?>
                                                    <span class="text-warning">₹<?php echo number_format($serviceCharge, 2); ?></span>
                                                    <small class="text-muted d-block">(10%)</small>
                                                <?php else: ?>
                                                    <span class="text-muted">₹0.00</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $tdsDeduction = $log['tds_deduction'] ?? 0;
                                                if ($tdsDeduction > 0): ?>
                                                    <span class="text-info">₹<?php echo number_format($tdsDeduction, 2); ?></span>
                                                    <small class="text-muted d-block">(5%)</small>
                                                <?php else: ?>
                                                    <span class="text-muted">₹0.00</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $netIncome = $log['net_income'] ?? $log['income_amount'] ?? 0;
                                                if ($netIncome > 0): ?>
                                                    <strong class="text-success">₹<?php echo number_format($netIncome, 2); ?></strong>
                                                    <small class="text-muted d-block">credited</small>
                                                <?php else: ?>
                                                    <span class="text-muted">₹0.00</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (($log['weekly_capping_applied'] ?? 0) > 0): ?>
                                                    <span class="text-warning">₹<?php echo number_format($log['weekly_capping_applied'], 2); ?></span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small>
                                                    <div><strong>L:</strong> <?php echo number_format($log['carry_forward_left'], 0); ?></div>
                                                    <div><strong>R:</strong> <?php echo number_format($log['carry_forward_right'], 0); ?></div>
                                                </small>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
