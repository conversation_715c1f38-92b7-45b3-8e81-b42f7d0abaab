<?php
/**
 * PV Usage Tracker
 * Tracks which PV contributions have been used for matching income calculation
 * Prevents duplicate income generation from the same PV
 */

require_once 'config/Connection.php';

class PVUsageTracker {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Track PV usage for a specific week
     * 
     * @param string $userId User ID
     * @param array $pvTransactions Array of PV transaction IDs and amounts used
     * @param string $weekStartDate Week start date (Y-m-d)
     * @param string $weekEndDate Week end date (Y-m-d)
     * @param int $incomeLogId Income log ID for reference
     * @return bool Success status
     */
    public function trackPVUsage($userId, $pvTransactions, $weekStartDate, $weekEndDate, $incomeLogId = null) {
        try {
            $this->db->beginTransaction();
            
            $stmt = $this->db->prepare("
                INSERT INTO pv_usage_tracking 
                (user_id, pv_transaction_id, used_amount, week_start_date, week_end_date, income_log_id) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($pvTransactions as $transaction) {
                $stmt->execute([
                    $userId,
                    $transaction['pv_transaction_id'],
                    $transaction['used_amount'],
                    $weekStartDate,
                    $weekEndDate,
                    $incomeLogId
                ]);
            }
            
            $this->db->commit();
            return true;
            
        } catch (Exception $e) {
            $this->db->rollback();
            error_log("PV Usage Tracking Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get available PV for a user that hasn't been used for matching income
     * 
     * @param string $userId User ID
     * @param string $side PV side ('left' or 'right')
     * @param string $beforeDate Only consider PV transactions before this date
     * @return array Available PV transactions
     */
    public function getAvailablePV($userId, $side, $beforeDate = null) {
        try {
            $beforeClause = $beforeDate ? "AND pt.created_at <= ?" : "";
            $params = [$userId, $side];
            if ($beforeDate) {
                $params[] = $beforeDate;
            }
            
            $stmt = $this->db->prepare("
                SELECT 
                    pt.id as pv_transaction_id,
                    pt.pv_amount,
                    pt.created_at,
                    COALESCE(SUM(put.used_amount), 0) as used_amount,
                    (pt.pv_amount - COALESCE(SUM(put.used_amount), 0)) as available_amount
                FROM pv_transactions pt
                LEFT JOIN pv_usage_tracking put ON pt.id = put.pv_transaction_id
                WHERE pt.user_id = ? 
                AND pt.side = ? 
                {$beforeClause}
                GROUP BY pt.id
                HAVING available_amount > 0
                ORDER BY pt.created_at ASC
            ");
            
            $stmt->execute($params);
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Get Available PV Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get downline available PV for a user
     * 
     * @param string $userId User ID
     * @param string $side PV side ('left' or 'right')
     * @param string $beforeDate Only consider PV transactions before this date
     * @return array Available downline PV
     */
    public function getDownlineAvailablePV($userId, $side, $beforeDate = null) {
        try {
            // Get all downline users
            $downlineUsers = $this->getDownlineUsers($userId, $side);
            
            if (empty($downlineUsers)) {
                return [];
            }
            
            $placeholders = str_repeat('?,', count($downlineUsers) - 1) . '?';
            $beforeClause = $beforeDate ? "AND pt.created_at <= ?" : "";
            
            $params = array_merge($downlineUsers, [$side]);
            if ($beforeDate) {
                $params[] = $beforeDate;
            }
            
            $stmt = $this->db->prepare("
                SELECT 
                    pt.id as pv_transaction_id,
                    pt.user_id,
                    pt.pv_amount,
                    pt.created_at,
                    COALESCE(SUM(put.used_amount), 0) as used_amount,
                    (pt.pv_amount - COALESCE(SUM(put.used_amount), 0)) as available_amount
                FROM pv_transactions pt
                LEFT JOIN pv_usage_tracking put ON pt.id = put.pv_transaction_id
                WHERE pt.user_id IN ({$placeholders})
                AND pt.side = ?
                {$beforeClause}
                GROUP BY pt.id
                HAVING available_amount > 0
                ORDER BY pt.created_at ASC
            ");
            
            $stmt->execute($params);
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Get Downline Available PV Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Calculate total available PV for a user (own + downline)
     * 
     * @param string $userId User ID
     * @param string $beforeDate Only consider PV transactions before this date
     * @return array Available PV totals by side
     */
    public function calculateAvailablePVTotals($userId, $beforeDate = null) {
        try {
            $totals = [
                'left_pv' => 0,
                'right_pv' => 0,
                'left_transactions' => [],
                'right_transactions' => []
            ];
            
            // Get own available PV
            $leftPV = $this->getAvailablePV($userId, 'left', $beforeDate);
            $rightPV = $this->getAvailablePV($userId, 'right', $beforeDate);
            
            // Get downline available PV
            $downlineLeftPV = $this->getDownlineAvailablePV($userId, 'left', $beforeDate);
            $downlineRightPV = $this->getDownlineAvailablePV($userId, 'right', $beforeDate);
            
            // Combine and calculate totals
            $totals['left_transactions'] = array_merge($leftPV, $downlineLeftPV);
            $totals['right_transactions'] = array_merge($rightPV, $downlineRightPV);
            
            $totals['left_pv'] = array_sum(array_column($totals['left_transactions'], 'available_amount'));
            $totals['right_pv'] = array_sum(array_column($totals['right_transactions'], 'available_amount'));
            
            return $totals;
            
        } catch (Exception $e) {
            error_log("Calculate Available PV Totals Error: " . $e->getMessage());
            return ['left_pv' => 0, 'right_pv' => 0, 'left_transactions' => [], 'right_transactions' => []];
        }
    }
    
    /**
     * Get downline users for a specific side
     * 
     * @param string $userId User ID
     * @param string $side Side ('left' or 'right')
     * @return array Downline user IDs
     */
    private function getDownlineUsers($userId, $side) {
        try {
            $downlineUsers = [];
            $queue = [$userId];
            $processed = [];
            
            while (!empty($queue)) {
                $currentUserId = array_shift($queue);
                
                if (in_array($currentUserId, $processed)) {
                    continue;
                }
                $processed[] = $currentUserId;
                
                // Get children based on side
                $childColumn = $side === 'left' ? 'left_child' : 'right_child';
                
                $stmt = $this->db->prepare("SELECT {$childColumn} as child_id FROM binary_tree WHERE user_id = ? AND {$childColumn} IS NOT NULL");
                $stmt->execute([$currentUserId]);
                $children = $stmt->fetchAll();
                
                foreach ($children as $child) {
                    if ($child['child_id'] && !in_array($child['child_id'], $processed)) {
                        $downlineUsers[] = $child['child_id'];
                        $queue[] = $child['child_id'];
                    }
                }
            }
            
            return $downlineUsers;
            
        } catch (Exception $e) {
            error_log("Get Downline Users Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get PV usage history for a user
     * 
     * @param string $userId User ID
     * @param int $limit Number of records to return
     * @return array PV usage history
     */
    public function getPVUsageHistory($userId, $limit = 10) {
        try {
            $stmt = $this->db->prepare("
                SELECT 
                    put.*,
                    pt.pv_amount as original_amount,
                    pt.side,
                    pt.description as transaction_description
                FROM pv_usage_tracking put
                JOIN pv_transactions pt ON put.pv_transaction_id = pt.id
                WHERE put.user_id = ?
                ORDER BY put.created_at DESC
                LIMIT ?
            ");
            
            $stmt->execute([$userId, $limit]);
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Get PV Usage History Error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Check if a specific week has been processed for a user
     * 
     * @param string $userId User ID
     * @param string $weekStartDate Week start date
     * @return bool True if week has been processed
     */
    public function isWeekProcessed($userId, $weekStartDate) {
        try {
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as count 
                FROM pv_usage_tracking 
                WHERE user_id = ? AND week_start_date = ?
            ");
            
            $stmt->execute([$userId, $weekStartDate]);
            $result = $stmt->fetch();
            
            return $result['count'] > 0;
            
        } catch (Exception $e) {
            error_log("Check Week Processed Error: " . $e->getMessage());
            return false;
        }
    }
}
