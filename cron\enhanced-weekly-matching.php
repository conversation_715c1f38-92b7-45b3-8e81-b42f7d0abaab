<?php
/**
 * Enhanced Weekly PV Matching Cron Job with Deduction System
 * MLM Binary Plan System
 * 
 * This script runs every Saturday at 12:00 AM (midnight) to process
 * weekly PV matching income with 10% service charge and 5% TDS deductions
 * 
 * Schedule: 0 0 * * 6 (Every Saturday at midnight)
 */

// Prevent direct browser access
if (php_sapi_name() !== 'cli') {
    die('This script can only be run from command line');
}

// Set time limit and memory limit for long-running process
set_time_limit(0);
ini_set('memory_limit', '1G');

echo str_repeat('=', 70) . "\n";
echo "Enhanced Weekly PV Matching Process with Deduction System\n";
echo "Start time: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat('=', 70) . "\n";

// Include required files
require_once dirname(__DIR__) . '/config/database.php';
require_once dirname(__DIR__) . '/config/Connection.php';
require_once dirname(__DIR__) . '/includes/PVSystem.php';
require_once dirname(__DIR__) . '/includes/PVUsageTracker.php';
require_once dirname(__DIR__) . '/includes/BinaryTree.php';
require_once dirname(__DIR__) . '/config/config.php';
require_once dirname(__DIR__) . '/includes/EmailService.php';

try {
    $pvSystem = new PVSystem();
    $config = Config::getInstance();
    $db = Database::getInstance();
    
    // Verify it's Saturday (day 6) - Allow bypass for testing
    $currentDayOfWeek = (int) date('w'); // 0 = Sunday, 6 = Saturday
    $forceRun = isset($argv[1]) && $argv[1] === '--force';

    if ($currentDayOfWeek !== 6 && !$forceRun) {
        echo "⚠️ This script should only run on Saturday (day 6). Current day: {$currentDayOfWeek}\n";
        echo "To run for testing purposes, use: php cron/enhanced-weekly-matching.php --force\n";
        echo "Exiting without processing.\n";
        exit(0);
    }

    if ($forceRun) {
        echo "🧪 TESTING MODE: Running with --force flag (bypassing Saturday check)\n";
    }
    
    // Determine the week to process (Monday to Sunday of current week)
    $weekStartDate = date('Y-m-d', strtotime('monday this week'));
    $weekEndDate = date('Y-m-d', strtotime('sunday this week'));
    
    echo "📅 Processing week: {$weekStartDate} to {$weekEndDate}\n";
    echo "🕐 Current time: " . date('Y-m-d H:i:s') . "\n\n";
    
    // Check if this week has already been processed
    $checkStmt = $db->prepare("SELECT id FROM weekly_income_reports WHERE week_start_date = ?");
    $checkStmt->execute([$weekStartDate]);
    
    if ($checkStmt->fetch()) {
        echo "⚠️ Week {$weekStartDate} to {$weekEndDate} has already been processed.\n";
        echo "Skipping weekly matching to prevent duplicate processing.\n";
        exit(0);
    }
    
    echo "🚀 Starting enhanced weekly PV matching process...\n\n";
    
    // Log the start of processing
    $logStmt = $db->prepare("INSERT INTO system_logs (log_type, message) VALUES (?, ?)");
    $logStmt->execute(['cron', "Enhanced weekly matching started for week {$weekStartDate} to {$weekEndDate}"]);
    
    // Run enhanced weekly matching for all users
    $result = $pvSystem->runWeeklyMatching($weekStartDate, $weekEndDate);
    
    if ($result !== false) {
        echo "\n" . str_repeat('-', 70) . "\n";
        echo "✅ Enhanced Weekly PV Matching Completed Successfully!\n";
        echo str_repeat('-', 70) . "\n";
        echo "📊 SUMMARY REPORT:\n";
        echo "   • Total users processed: " . number_format($result['processed']) . "\n";
        echo "   • Users with income: " . number_format($result['users_with_income']) . "\n";
        echo "   • Total gross income: ₹" . number_format($result['total_gross_income'], 2) . "\n";
        echo "   • Total service charges (10%): ₹" . number_format($result['total_service_charges'], 2) . "\n";
        echo "   • Total TDS deductions (5%): ₹" . number_format($result['total_tds_deductions'], 2) . "\n";
        echo "   • Total net income credited: ₹" . number_format($result['total_net_income'], 2) . "\n";
        echo "   • Total capping applied: ₹" . number_format($result['total_capping'], 2) . "\n";
        echo str_repeat('-', 70) . "\n";
        
        // Calculate deduction percentages for verification
        if ($result['total_gross_income'] > 0) {
            $serviceChargePercent = ($result['total_service_charges'] / $result['total_gross_income']) * 100;
            $tdsPercent = ($result['total_tds_deductions'] / $result['total_gross_income']) * 100;
            $netPercent = ($result['total_net_income'] / $result['total_gross_income']) * 100;
            
            echo "🔍 DEDUCTION VERIFICATION:\n";
            echo "   • Service charge rate: " . number_format($serviceChargePercent, 2) . "%\n";
            echo "   • TDS rate: " . number_format($tdsPercent, 2) . "%\n";
            echo "   • Net income rate: " . number_format($netPercent, 2) . "%\n";
            echo str_repeat('-', 70) . "\n";
        }
        
        // Log the successful completion
        $logMessage = sprintf(
            "Enhanced weekly matching completed successfully. Processed %d users, " .
            "Gross: ₹%s, Service Charges: ₹%s, TDS: ₹%s, Net: ₹%s",
            $result['processed'],
            number_format($result['total_gross_income'], 2),
            number_format($result['total_service_charges'], 2),
            number_format($result['total_tds_deductions'], 2),
            number_format($result['total_net_income'], 2)
        );
        
        $logStmt->execute(['cron', $logMessage]);
        
        // Send admin notification
        echo "📧 Sending admin notification...\n";
        $emailResult = sendEnhancedAdminNotification($weekStartDate, $weekEndDate, $result);
        
        if ($emailResult) {
            echo "✅ Admin notification sent successfully.\n";
            
            // Update report status to 'sent'
            $updateStmt = $db->prepare("UPDATE weekly_income_reports SET report_sent_at = NOW(), report_status = 'sent' WHERE week_start_date = ?");
            $updateStmt->execute([$weekStartDate]);
        } else {
            echo "⚠️ Failed to send admin notification.\n";
        }
        
    } else {
        echo "❌ Enhanced weekly matching failed!\n";
        $logStmt->execute(['error', "Enhanced weekly matching failed for week {$weekStartDate} to {$weekEndDate}"]);
        exit(1);
    }
    
    echo "\n🎉 Process completed at: " . date('Y-m-d H:i:s') . "\n";
    echo str_repeat('=', 70) . "\n";
    
} catch (Exception $e) {
    echo "❌ Fatal error during enhanced weekly matching: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    
    // Log the error
    try {
        $db = Database::getInstance();
        $logStmt = $db->prepare("INSERT INTO system_logs (log_type, message) VALUES (?, ?)");
        $logStmt->execute(['error', "Enhanced weekly matching fatal error: " . $e->getMessage()]);
    } catch (Exception $logError) {
        echo "Failed to log error: " . $logError->getMessage() . "\n";
    }
    
    exit(1);
}

/**
 * Send enhanced admin notification about weekly income processing
 */
function sendEnhancedAdminNotification($weekStartDate, $weekEndDate, $result) {
    try {
        $emailService = new EmailService();

        // Prepare enhanced email content
        $subject = "Enhanced Weekly Income Report - Week {$weekStartDate} to {$weekEndDate}";
        
        $emailContent = "
        <h2>Enhanced Weekly PV Matching Report</h2>
        <p><strong>Week Period:</strong> {$weekStartDate} to {$weekEndDate}</p>
        <p><strong>Processing Date:</strong> " . date('Y-m-d H:i:s') . "</p>
        
        <h3>Summary Statistics</h3>
        <ul>
            <li><strong>Total Users Processed:</strong> " . number_format($result['processed']) . "</li>
            <li><strong>Users with Income:</strong> " . number_format($result['users_with_income']) . "</li>
        </ul>
        
        <h3>Income Breakdown</h3>
        <ul>
            <li><strong>Total Gross Income:</strong> ₹" . number_format($result['total_gross_income'], 2) . "</li>
            <li><strong>Service Charges (10%):</strong> ₹" . number_format($result['total_service_charges'], 2) . "</li>
            <li><strong>TDS Deductions (5%):</strong> ₹" . number_format($result['total_tds_deductions'], 2) . "</li>
            <li><strong>Net Income Credited:</strong> ₹" . number_format($result['total_net_income'], 2) . "</li>
            <li><strong>Capping Applied:</strong> ₹" . number_format($result['total_capping'], 2) . "</li>
        </ul>
        
        <h3>Transparency Note</h3>
        <p>All deductions are clearly tracked and visible to users in their income history. 
        The system maintains complete audit trails for regulatory compliance.</p>
        ";

        // Send email notification
        $emailResult = $emailService->sendAdminEmail($subject, $emailContent);

        return $emailResult;
        
    } catch (Exception $e) {
        error_log("Enhanced admin notification error: " . $e->getMessage());
        return false;
    }
}
?>
