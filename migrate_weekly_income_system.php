<?php
/**
 * Migration Script for Enhanced Weekly Income System
 * Adds deduction tracking and PV usage tracking capabilities
 */

require_once 'config/database.php';
require_once 'config/Connection.php';

try {
    $db = Database::getInstance();
    
    echo "🚀 Starting Enhanced Weekly Income System Migration...\n\n";

    // Create backup recommendation
    echo "📋 PRE-MIGRATION CHECKLIST:\n";
    echo "1. ✅ Database backup recommended before proceeding\n";
    echo "2. ✅ Ensure no active weekly processing is running\n";
    echo "3. ✅ Verify sufficient disk space for new tables\n";
    echo "4. ✅ Check current database size and structure\n\n";

    // Check current database state
    echo "🔍 Checking current database state...\n";

    // Check if migration has already been run
    $checkStmt = $db->prepare("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = 'pv_usage_tracking'");
    $checkStmt->execute([DB_NAME]);
    $result = $checkStmt->fetch();

    $migrationAlreadyRun = $result['count'] > 0;

    if ($migrationAlreadyRun) {
        echo "⚠️ Migration appears to have already been run. Checking for missing components...\n";
    } else {
        echo "✅ Fresh migration - proceeding with full setup...\n";
    }

    // Check existing data
    $existingDataCheck = [
        'users' => "SELECT COUNT(*) as count FROM users",
        'weekly_income_logs' => "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = 'weekly_income_logs'",
        'pv_transactions' => "SELECT COUNT(*) as count FROM pv_transactions"
    ];

    echo "\n📊 Current database state:\n";
    foreach ($existingDataCheck as $table => $query) {
        try {
            if ($table === 'weekly_income_logs') {
                $stmt = $db->prepare($query);
                $stmt->execute([DB_NAME]);
            } else {
                $stmt = $db->prepare($query);
                $stmt->execute();
            }
            $data = $stmt->fetch();
            echo "• {$table}: " . ($data['count'] ?? 0) . " records/exists\n";
        } catch (Exception $e) {
            echo "• {$table}: Not found or error\n";
        }
    }
    echo "\n";
    
    // Migration steps
    $migrations = [
        // Step 1: Create PV Usage Tracking table
        [
            'name' => 'Create PV Usage Tracking table',
            'sql' => "CREATE TABLE IF NOT EXISTS pv_usage_tracking (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id VARCHAR(20) NOT NULL,
                pv_transaction_id INT NOT NULL,
                used_amount DECIMAL(10,2) NOT NULL,
                week_start_date DATE NOT NULL,
                week_end_date DATE NOT NULL,
                income_log_id INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
                FOREIGN KEY (pv_transaction_id) REFERENCES pv_transactions(id) ON DELETE CASCADE,
                INDEX idx_user_week (user_id, week_start_date),
                INDEX idx_pv_transaction (pv_transaction_id),
                INDEX idx_week_dates (week_start_date, week_end_date)
            )"
        ],
        
        // Step 2: Create Enhanced Weekly Income Logs table
        [
            'name' => 'Create Enhanced Weekly Income Logs table',
            'sql' => "CREATE TABLE IF NOT EXISTS weekly_income_logs (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id VARCHAR(20) NOT NULL,
                week_start_date DATE NOT NULL,
                week_end_date DATE NOT NULL,
                left_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                right_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                matched_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                gross_income DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                service_charge DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                tds_deduction DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                net_income DECIMAL(10,2) NOT NULL DEFAULT 0.00,
                weekly_capping_applied DECIMAL(10,2) DEFAULT 0.00,
                carry_forward_left DECIMAL(10,2) DEFAULT 0.00,
                carry_forward_right DECIMAL(10,2) DEFAULT 0.00,
                processing_status ENUM('pending', 'processed', 'failed') DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
                UNIQUE KEY unique_user_week (user_id, week_start_date),
                INDEX idx_week_start_date (week_start_date),
                INDEX idx_user_week (user_id, week_start_date),
                INDEX idx_net_income (net_income),
                INDEX idx_processing_status (processing_status)
            )"
        ],
        
        // Step 3: Create Enhanced Weekly Income Reports table
        [
            'name' => 'Create Enhanced Weekly Income Reports table',
            'sql' => "CREATE TABLE IF NOT EXISTS weekly_income_reports (
                id INT PRIMARY KEY AUTO_INCREMENT,
                week_start_date DATE NOT NULL,
                week_end_date DATE NOT NULL,
                total_users_processed INT DEFAULT 0,
                total_users_earned INT DEFAULT 0,
                total_gross_income DECIMAL(12,2) DEFAULT 0.00,
                total_service_charges DECIMAL(12,2) DEFAULT 0.00,
                total_tds_deductions DECIMAL(12,2) DEFAULT 0.00,
                total_net_income DECIMAL(12,2) DEFAULT 0.00,
                total_capping_applied DECIMAL(12,2) DEFAULT 0.00,
                report_status ENUM('processing', 'generated', 'sent', 'failed') DEFAULT 'processing',
                report_generated_at TIMESTAMP NULL,
                report_sent_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_week (week_start_date, week_end_date),
                INDEX idx_week_start_end (week_start_date, week_end_date),
                INDEX idx_report_status (report_status),
                INDEX idx_generated_at (report_generated_at)
            )"
        ],
        
        // Step 4: Add new configuration values
        [
            'name' => 'Add deduction configuration values',
            'sql' => "INSERT IGNORE INTO config (config_key, config_value, description) VALUES 
                ('weekly_capping', '130000.00', 'Maximum weekly income per user in INR'),
                ('service_charge_rate', '10.00', 'Service charge percentage (10%)'),
                ('tds_rate', '5.00', 'TDS deduction percentage (5%)')"
        ],
        
        // Step 5: Update wallet_transactions reference types
        [
            'name' => 'Update wallet transaction reference types',
            'sql' => "ALTER TABLE wallet_transactions MODIFY COLUMN reference_type ENUM('pv_matching', 'weekly_matching', 'withdrawal', 'bonus', 'manual', 'service_charge', 'tds') NOT NULL"
        ],

        // Step 6: Migrate existing weekly income data (if any)
        [
            'name' => 'Migrate existing weekly income data',
            'sql' => "INSERT IGNORE INTO weekly_income_logs
                     (user_id, week_start_date, week_end_date, left_pv, right_pv, matched_pv,
                      gross_income, service_charge, tds_deduction, net_income, weekly_capping_applied,
                      carry_forward_left, carry_forward_right, processing_status)
                     SELECT user_id,
                            DATE(matching_date) as week_start_date,
                            DATE(matching_date) as week_end_date,
                            left_pv, right_pv, matched_pv,
                            income_amount as gross_income,
                            0 as service_charge,
                            0 as tds_deduction,
                            income_amount as net_income,
                            capping_applied as weekly_capping_applied,
                            carry_forward_left, carry_forward_right,
                            'processed' as processing_status
                     FROM income_logs
                     WHERE matching_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                     ON DUPLICATE KEY UPDATE processing_status = 'processed'"
        ]
    ];
    
    // Execute migrations
    $successCount = 0;
    $totalMigrations = count($migrations);
    
    foreach ($migrations as $index => $migration) {
        echo "Step " . ($index + 1) . "/{$totalMigrations}: {$migration['name']}...\n";
        
        try {
            $db->exec($migration['sql']);
            echo "✅ Success\n";
            $successCount++;
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'already exists') !== false || 
                strpos($e->getMessage(), 'Duplicate entry') !== false) {
                echo "ℹ️ Already exists, skipping\n";
                $successCount++;
            } else {
                echo "❌ Error: " . $e->getMessage() . "\n";
            }
        }
        echo "\n";
    }
    
    echo str_repeat('-', 50) . "\n";
    echo "Migration Summary:\n";
    echo "✅ Successful: {$successCount}/{$totalMigrations}\n";
    echo "❌ Failed: " . ($totalMigrations - $successCount) . "/{$totalMigrations}\n";
    
    if ($successCount === $totalMigrations) {
        echo "\n🎉 Enhanced Weekly Income System migration completed successfully!\n";

        // Post-migration validation
        echo "\n🔍 Running post-migration validation...\n";

        $validationQueries = [
            'PV Usage Tracking table' => "SELECT COUNT(*) as count FROM pv_usage_tracking",
            'Weekly Income Logs table' => "SELECT COUNT(*) as count FROM weekly_income_logs",
            'Weekly Income Reports table' => "SELECT COUNT(*) as count FROM weekly_income_reports",
            'Enhanced config values' => "SELECT COUNT(*) as count FROM config WHERE config_key IN ('weekly_capping', 'service_charge_rate', 'tds_rate')"
        ];

        $validationPassed = true;
        foreach ($validationQueries as $name => $query) {
            try {
                $stmt = $db->prepare($query);
                $stmt->execute();
                $result = $stmt->fetch();
                echo "✅ {$name}: " . ($result['count'] ?? 0) . " records\n";
            } catch (Exception $e) {
                echo "❌ {$name}: Validation failed - " . $e->getMessage() . "\n";
                $validationPassed = false;
            }
        }

        if ($validationPassed) {
            echo "\n✅ All validation checks passed!\n";
        } else {
            echo "\n⚠️ Some validation checks failed. Please review.\n";
        }

        echo "\n🚀 NEW FEATURES ADDED:\n";
        echo "- PV usage tracking to prevent duplicate income\n";
        echo "- 10% service charge deduction\n";
        echo "- 5% TDS deduction\n";
        echo "- Enhanced reporting with deduction breakdown\n";
        echo "- Improved audit trail and transparency\n";
        echo "- Performance optimized database structure\n";

        echo "\n📋 NEXT STEPS:\n";
        echo "1. Run the performance optimization script: php optimize_enhanced_weekly_system.php\n";
        echo "2. Set up the enhanced cron job: php setup_cron.php\n";
        echo "3. Test the enhanced weekly matching: php cron/enhanced-weekly-matching.php\n";
        echo "4. Review admin and user dashboards for new features\n";

        echo "\n⚠️ ROLLBACK INSTRUCTIONS (if needed):\n";
        echo "If you need to rollback this migration:\n";
        echo "1. DROP TABLE pv_usage_tracking;\n";
        echo "2. DROP TABLE weekly_income_logs;\n";
        echo "3. DROP TABLE weekly_income_reports;\n";
        echo "4. DELETE FROM config WHERE config_key IN ('weekly_capping', 'service_charge_rate', 'tds_rate');\n";
        echo "5. Restore from backup if necessary\n";

    } else {
        echo "\n⚠️ Migration completed with some issues. Please review the errors above.\n";
        echo "\n🔧 TROUBLESHOOTING:\n";
        echo "- Check database permissions\n";
        echo "- Verify table structure compatibility\n";
        echo "- Review error messages for specific issues\n";
        echo "- Consider running individual migration steps manually\n";
    }
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
