<?php
/**
 * Create Test Data for Weekly Income System
 * Generates sample PV transactions and users for testing
 */

require_once 'config/database.php';
require_once 'config/Connection.php';

try {
    $db = Database::getInstance();
    
    echo "🧪 Creating Test Data for Weekly Income System...\n\n";
    
    // Check if we should create test data
    $pvCountStmt = $db->query("SELECT COUNT(*) as count FROM pv_transactions");
    $pvCount = $pvCountStmt->fetch()['count'];
    
    $userCountStmt = $db->query("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
    $userCount = $userCountStmt->fetch()['count'];
    
    echo "Current state:\n";
    echo "• Active users: {$userCount}\n";
    echo "• PV transactions: {$pvCount}\n\n";
    
    if ($pvCount > 0) {
        echo "⚠️ PV transactions already exist. Do you want to add more test data? (y/n): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        if (trim($line) !== 'y') {
            echo "Skipping test data creation.\n";
            exit(0);
        }
        fclose($handle);
    }
    
    // Create test users if needed
    if ($userCount < 5) {
        echo "1. Creating test users...\n";
        
        $testUsers = [
            ['TEST001', 'Test User 1', '<EMAIL>', '1234567890'],
            ['TEST002', 'Test User 2', '<EMAIL>', '1234567891'],
            ['TEST003', 'Test User 3', '<EMAIL>', '1234567892'],
            ['TEST004', 'Test User 4', '<EMAIL>', '1234567893'],
            ['TEST005', 'Test User 5', '<EMAIL>', '1234567894']
        ];
        
        foreach ($testUsers as $user) {
            try {
                $stmt = $db->prepare("INSERT IGNORE INTO users (user_id, full_name, email, phone, password, status, registration_date) VALUES (?, ?, ?, ?, ?, 'active', NOW())");
                $stmt->execute([$user[0], $user[1], $user[2], $user[3], 'password123']);
                echo "   ✅ Created user: {$user[0]} - {$user[1]}\n";
                
                // Create wallet for user
                $walletStmt = $db->prepare("INSERT IGNORE INTO wallet (user_id, balance) VALUES (?, 0.00)");
                $walletStmt->execute([$user[0]]);
                
                // Create binary tree entry
                $treeStmt = $db->prepare("INSERT IGNORE INTO binary_tree (user_id, sponsor_id, position, level) VALUES (?, 'ADMIN001', 'left', 1)");
                $treeStmt->execute([$user[0]]);
                
            } catch (Exception $e) {
                echo "   ⚠️ User {$user[0]} might already exist\n";
            }
        }
        echo "\n";
    }
    
    // Create test PV transactions
    echo "2. Creating test PV transactions...\n";
    
    $testPVData = [
        ['TEST001', 1000, 'left', 'Test PV for left side'],
        ['TEST001', 800, 'right', 'Test PV for right side'],
        ['TEST002', 1500, 'left', 'Test PV for left side'],
        ['TEST002', 1200, 'right', 'Test PV for right side'],
        ['TEST003', 2000, 'left', 'Test PV for left side'],
        ['TEST003', 1800, 'right', 'Test PV for right side'],
        ['TEST004', 500, 'left', 'Test PV for left side'],
        ['TEST004', 600, 'right', 'Test PV for right side'],
        ['TEST005', 1200, 'left', 'Test PV for left side'],
        ['TEST005', 1000, 'right', 'Test PV for right side']
    ];
    
    foreach ($testPVData as $pv) {
        try {
            $stmt = $db->prepare("INSERT INTO pv_transactions (user_id, transaction_type, pv_amount, side, description, created_at) VALUES (?, 'manual', ?, ?, ?, NOW())");
            $stmt->execute([$pv[0], $pv[1], $pv[2], $pv[3]]);
            echo "   ✅ Added {$pv[1]} PV to {$pv[0]} ({$pv[2]} side)\n";
        } catch (Exception $e) {
            echo "   ❌ Error adding PV to {$pv[0]}: " . $e->getMessage() . "\n";
        }
    }
    echo "\n";
    
    // Show PV summary
    echo "3. PV Summary after test data creation...\n";
    
    $summaryStmt = $db->query("
        SELECT user_id, 
               SUM(CASE WHEN side = 'left' THEN pv_amount ELSE 0 END) as left_pv,
               SUM(CASE WHEN side = 'right' THEN pv_amount ELSE 0 END) as right_pv
        FROM pv_transactions 
        GROUP BY user_id 
        HAVING left_pv > 0 OR right_pv > 0
        ORDER BY user_id
    ");
    $summary = $summaryStmt->fetchAll();
    
    foreach ($summary as $user) {
        $matchable = min($user['left_pv'], $user['right_pv']);
        echo "   • {$user['user_id']}: Left {$user['left_pv']}, Right {$user['right_pv']}, Matchable: {$matchable}\n";
    }
    echo "\n";
    
    // Calculate potential income
    echo "4. Potential Weekly Income Calculation...\n";
    
    $totalMatchable = 0;
    foreach ($summary as $user) {
        $matchable = min($user['left_pv'], $user['right_pv']);
        $totalMatchable += $matchable;
    }
    
    // Assuming PV rate of 0.10 (₹0.10 per PV)
    $pvRate = 0.10;
    $totalGrossIncome = $totalMatchable * $pvRate;
    $totalServiceCharge = $totalGrossIncome * 0.10; // 10%
    $totalTDS = $totalGrossIncome * 0.05; // 5%
    $totalNetIncome = $totalGrossIncome - $totalServiceCharge - $totalTDS;
    
    echo "   📊 Potential Income Summary:\n";
    echo "   • Total Matchable PV: {$totalMatchable}\n";
    echo "   • Gross Income: ₹" . number_format($totalGrossIncome, 2) . "\n";
    echo "   • Service Charge (10%): ₹" . number_format($totalServiceCharge, 2) . "\n";
    echo "   • TDS (5%): ₹" . number_format($totalTDS, 2) . "\n";
    echo "   • Net Income: ₹" . number_format($totalNetIncome, 2) . "\n\n";
    
    // Instructions for next steps
    echo str_repeat('=', 60) . "\n";
    echo "🎉 TEST DATA CREATED SUCCESSFULLY!\n";
    echo str_repeat('=', 60) . "\n";
    
    echo "📋 NEXT STEPS:\n\n";
    
    echo "1. Generate Weekly Income:\n";
    echo "   • Go to Admin Panel → Weekly Income Reports\n";
    echo "   • Click 'Generate Report'\n";
    echo "   • Select current week dates\n";
    echo "   • Click 'Generate Report'\n\n";
    
    echo "2. Or run via command line:\n";
    echo "   php cron/enhanced-weekly-matching.php\n\n";
    
    echo "3. Check results:\n";
    echo "   • Admin Dashboard: Weekly Income Reports\n";
    echo "   • User Dashboard: Income History\n\n";
    
    echo "4. Test users created:\n";
    foreach ($testUsers as $user) {
        echo "   • {$user[0]} - {$user[1]} (password: password123)\n";
    }
    echo "\n";
    
    echo "✅ Test data is ready! You can now test the weekly income system.\n";
    
} catch (Exception $e) {
    echo "❌ Test data creation failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
