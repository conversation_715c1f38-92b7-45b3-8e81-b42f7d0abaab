<?php
/**
 * Database Optimization Script
 * MLM Binary Plan System
 * 
 * This script adds indexes to frequently queried columns in all tables
 * to improve database performance.
 */

require_once 'config/database.php';
require_once 'config/Connection.php';

// Set time limit for long-running process
set_time_limit(0);

echo "=== Database Optimization Script ===\n";
echo "Start time: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $db = Database::getInstance();
    
    // Start transaction
    $db->beginTransaction();
    
    // Array of indexes to add
    $indexes = [
        // users table
        "ALTER TABLE users ADD INDEX idx_email (email)",
        "ALTER TABLE users ADD INDEX idx_phone (phone)",
        "ALTER TABLE users ADD INDEX idx_status (status)",
        "ALTER TABLE users ADD INDEX idx_sponsor_placement (sponsor_id, placement_side)",
        "ALTER TABLE users ADD INDEX idx_franchise (franchise_id)",
        "ALTER TABLE users ADD INDEX idx_registration_date (registration_date)",
        
        // binary_tree table
        "ALTER TABLE binary_tree ADD INDEX idx_left_child (left_child)",
        "ALTER TABLE binary_tree ADD INDEX idx_right_child (right_child)",
        "ALTER TABLE binary_tree ADD INDEX idx_position (position)",
        "ALTER TABLE binary_tree ADD INDEX idx_parent_position (parent_id, position)",
        
        // pv_transactions table
        "ALTER TABLE pv_transactions ADD INDEX idx_transaction_type (transaction_type)",
        "ALTER TABLE pv_transactions ADD INDEX idx_user_transaction_type (user_id, transaction_type)",
        "ALTER TABLE pv_transactions ADD INDEX idx_user_side_date (user_id, side, created_at)",
        "ALTER TABLE pv_transactions ADD INDEX idx_product (product_id)",
        "ALTER TABLE pv_transactions ADD INDEX idx_reference (reference_id)",
        
        // wallet table
        "ALTER TABLE wallet ADD INDEX idx_balance (balance)",
        "ALTER TABLE wallet ADD INDEX idx_total_earned (total_earned)",
        
        // wallet_transactions table
        "ALTER TABLE wallet_transactions ADD INDEX idx_transaction_type (transaction_type)",
        "ALTER TABLE wallet_transactions ADD INDEX idx_reference_type (reference_type)",
        "ALTER TABLE wallet_transactions ADD INDEX idx_user_date (user_id, created_at)",
        
        // withdrawals table
        "ALTER TABLE withdrawals ADD INDEX idx_amount (amount)",
        "ALTER TABLE withdrawals ADD INDEX idx_requested_at (requested_at)",
        "ALTER TABLE withdrawals ADD INDEX idx_processed_at (processed_at)",
        
        // income_logs table
        "ALTER TABLE income_logs ADD INDEX idx_matching_date_user (matching_date, user_id)",
        "ALTER TABLE income_logs ADD INDEX idx_income_amount (income_amount)",
        
        // login_logs table
        "ALTER TABLE login_logs ADD INDEX idx_ip_address (ip_address)",
        "ALTER TABLE login_logs ADD INDEX idx_status_date (status, login_time)",
        
        // purchase_orders table
        "ALTER TABLE purchase_orders ADD INDEX idx_order_id (order_id)",
        "ALTER TABLE purchase_orders ADD INDEX idx_payment_id (payment_id)",
        "ALTER TABLE purchase_orders ADD INDEX idx_order_payment_status (order_status, payment_status)",
        "ALTER TABLE purchase_orders ADD INDEX idx_created_at (created_at)",
        
        // products table
        "ALTER TABLE products ADD INDEX idx_product_code (product_code)",
        "ALTER TABLE products ADD INDEX idx_price (price)",
        "ALTER TABLE products ADD INDEX idx_pv_value (pv_value)",
        "ALTER TABLE products ADD INDEX idx_status (status)",
        
        // franchise table
        "ALTER TABLE franchise ADD INDEX idx_franchise_code (franchise_code)",
        "ALTER TABLE franchise ADD INDEX idx_commission_rate (commission_rate)",
        "ALTER TABLE franchise ADD INDEX idx_status (status)",
        
        // admin table
        "ALTER TABLE admin ADD INDEX idx_status (status)",
        
        // system_logs table
        "ALTER TABLE system_logs ADD INDEX idx_log_type_date (log_type, created_at)",
        
        // weekly_income_logs table (if exists)
        "ALTER TABLE weekly_income_logs ADD INDEX idx_week_start_date (week_start_date)",
        "ALTER TABLE weekly_income_logs ADD INDEX idx_user_week (user_id, week_start_date)",
        "ALTER TABLE weekly_income_logs ADD INDEX idx_income_amount (income_amount)",
        
        // weekly_income_reports table (if exists)
        "ALTER TABLE weekly_income_reports ADD INDEX idx_week_start_end (week_start_date, week_end_date)",
        "ALTER TABLE weekly_income_reports ADD INDEX idx_report_status (report_status)",
        "ALTER TABLE weekly_income_reports ADD INDEX idx_income_summary (week_start_date, total_gross_income, total_net_income)",

        // pv_usage_tracking table (enhanced weekly system)
        "ALTER TABLE pv_usage_tracking ADD INDEX idx_user_week_optimized (user_id, week_start_date, week_end_date)",
        "ALTER TABLE pv_usage_tracking ADD INDEX idx_pv_transaction_lookup (pv_transaction_id, used_amount)",
        "ALTER TABLE pv_usage_tracking ADD INDEX idx_week_range (week_start_date, week_end_date, user_id)",

        // Enhanced weekly_income_logs indexes
        "ALTER TABLE weekly_income_logs ADD INDEX idx_processing_status_week (processing_status, week_start_date)",
        "ALTER TABLE weekly_income_logs ADD INDEX idx_user_income_lookup (user_id, net_income, week_start_date)",
        "ALTER TABLE weekly_income_logs ADD INDEX idx_deduction_analysis (week_start_date, gross_income, service_charge, tds_deduction)",

        // Enhanced wallet_transactions indexes for deduction tracking
        "ALTER TABLE wallet_transactions ADD INDEX idx_deduction_tracking (user_id, reference_type, created_at, amount)",
        "ALTER TABLE wallet_transactions ADD INDEX idx_weekly_matching (reference_type, reference_id, created_at)"
    ];
    
    // Execute each index creation query
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($indexes as $indexQuery) {
        try {
            $db->exec($indexQuery);
            echo "✅ " . $indexQuery . "\n";
            $successCount++;
        } catch (PDOException $e) {
            // Skip if index already exists or table doesn't exist
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "ℹ️ Index already exists: " . $indexQuery . "\n";
            } elseif (strpos($e->getMessage(), "doesn't exist") !== false) {
                echo "ℹ️ Table doesn't exist for: " . $indexQuery . "\n";
            } else {
                echo "❌ Error: " . $e->getMessage() . " for query: " . $indexQuery . "\n";
                $errorCount++;
            }
        }
    }
    
    // Optimize tables (including enhanced weekly system tables)
    $tables = [
        'users', 'binary_tree', 'pv_transactions', 'wallet', 'wallet_transactions',
        'withdrawals', 'income_logs', 'login_logs', 'purchase_orders', 'products',
        'franchise', 'admin', 'system_logs', 'weekly_income_logs', 'weekly_income_reports',
        'pv_usage_tracking', 'config'
    ];
    
    foreach ($tables as $table) {
        try {
            $db->exec("OPTIMIZE TABLE $table");
            echo "✅ Optimized table: $table\n";
        } catch (PDOException $e) {
            // Skip if table doesn't exist
            if (strpos($e->getMessage(), "doesn't exist") !== false) {
                echo "ℹ️ Table doesn't exist: $table\n";
            } else {
                echo "❌ Error optimizing table $table: " . $e->getMessage() . "\n";
            }
        }
    }
    
    // Commit transaction
    $db->commit();
    
    echo "\n=== Database Optimization Complete ===\n";
    echo "Successfully added $successCount indexes\n";
    echo "Errors encountered: $errorCount\n";
    echo "End time: " . date('Y-m-d H:i:s') . "\n";
    
} catch (Exception $e) {
    // Rollback transaction on error
    if ($db->inTransaction()) {
        $db->rollback();
    }
    
    echo "❌ Fatal error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
