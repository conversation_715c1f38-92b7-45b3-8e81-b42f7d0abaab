<?php
/**
 * Fix Migration Issue - Complete the Enhanced Weekly Income System Setup
 * This script fixes the migration issue and ensures all tables are created properly
 */

require_once 'config/database.php';
require_once 'config/Connection.php';

try {
    $db = Database::getInstance();
    
    echo "🔧 Fixing Enhanced Weekly Income System Migration Issue...\n\n";
    
    // Step 1: Ensure all required tables exist
    echo "1. Creating/Verifying Required Tables...\n";
    
    $tables = [
        'pv_usage_tracking' => "CREATE TABLE IF NOT EXISTS pv_usage_tracking (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            pv_transaction_id INT NOT NULL,
            used_amount DECIMAL(10,2) NOT NULL,
            week_start_date DATE NOT NULL,
            week_end_date DATE NOT NULL,
            income_log_id INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (pv_transaction_id) REFERENCES pv_transactions(id) ON DELETE CASCADE,
            INDEX idx_user_week (user_id, week_start_date),
            INDEX idx_pv_transaction (pv_transaction_id),
            INDEX idx_week_dates (week_start_date, week_end_date)
        )",
        
        'weekly_income_logs' => "CREATE TABLE IF NOT EXISTS weekly_income_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            week_start_date DATE NOT NULL,
            week_end_date DATE NOT NULL,
            left_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            right_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            matched_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            gross_income DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            service_charge DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            tds_deduction DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            net_income DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            weekly_capping_applied DECIMAL(10,2) DEFAULT 0.00,
            carry_forward_left DECIMAL(10,2) DEFAULT 0.00,
            carry_forward_right DECIMAL(10,2) DEFAULT 0.00,
            processing_status ENUM('pending', 'processed', 'failed') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_week (user_id, week_start_date),
            INDEX idx_week_start_date (week_start_date),
            INDEX idx_user_week (user_id, week_start_date),
            INDEX idx_net_income (net_income),
            INDEX idx_processing_status (processing_status)
        )",
        
        'weekly_income_reports' => "CREATE TABLE IF NOT EXISTS weekly_income_reports (
            id INT PRIMARY KEY AUTO_INCREMENT,
            week_start_date DATE NOT NULL,
            week_end_date DATE NOT NULL,
            total_users_processed INT DEFAULT 0,
            total_users_earned INT DEFAULT 0,
            total_gross_income DECIMAL(12,2) DEFAULT 0.00,
            total_service_charges DECIMAL(12,2) DEFAULT 0.00,
            total_tds_deductions DECIMAL(12,2) DEFAULT 0.00,
            total_net_income DECIMAL(12,2) DEFAULT 0.00,
            total_capping_applied DECIMAL(12,2) DEFAULT 0.00,
            report_status ENUM('processing', 'generated', 'sent', 'failed') DEFAULT 'processing',
            report_generated_at TIMESTAMP NULL,
            report_sent_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_week (week_start_date, week_end_date),
            INDEX idx_week_start_end (week_start_date, week_end_date),
            INDEX idx_report_status (report_status),
            INDEX idx_generated_at (report_generated_at)
        )"
    ];
    
    foreach ($tables as $tableName => $sql) {
        try {
            $db->exec($sql);
            echo "✅ Table '{$tableName}' created/verified\n";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'already exists') !== false) {
                echo "ℹ️ Table '{$tableName}' already exists\n";
            } else {
                echo "❌ Error creating '{$tableName}': " . $e->getMessage() . "\n";
            }
        }
    }
    echo "\n";
    
    // Step 2: Add required configuration values
    echo "2. Adding Required Configuration Values...\n";
    
    $configs = [
        ['weekly_capping', '130000.00', 'Maximum weekly income per user in INR'],
        ['service_charge_rate', '10.00', 'Service charge percentage (10%)'],
        ['tds_rate', '5.00', 'TDS deduction percentage (5%)']
    ];
    
    foreach ($configs as $config) {
        try {
            $checkStmt = $db->prepare("SELECT COUNT(*) as count FROM config WHERE config_key = ?");
            $checkStmt->execute([$config[0]]);
            
            if ($checkStmt->fetch()['count'] == 0) {
                $insertStmt = $db->prepare("INSERT INTO config (config_key, config_value, description) VALUES (?, ?, ?)");
                $insertStmt->execute($config);
                echo "✅ Added config '{$config[0]}': {$config[1]}\n";
            } else {
                echo "ℹ️ Config '{$config[0]}' already exists\n";
            }
        } catch (PDOException $e) {
            echo "❌ Error adding config '{$config[0]}': " . $e->getMessage() . "\n";
        }
    }
    echo "\n";
    
    // Step 3: Update wallet_transactions reference types
    echo "3. Updating Wallet Transaction Reference Types...\n";
    
    try {
        $db->exec("ALTER TABLE wallet_transactions MODIFY COLUMN reference_type ENUM('pv_matching', 'weekly_matching', 'withdrawal', 'bonus', 'manual', 'service_charge', 'tds') NOT NULL");
        echo "✅ Wallet transaction reference types updated\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'already') !== false) {
            echo "ℹ️ Wallet transaction reference types already updated\n";
        } else {
            echo "❌ Error updating wallet transaction types: " . $e->getMessage() . "\n";
        }
    }
    echo "\n";
    
    // Step 4: Safe data migration (if possible)
    echo "4. Attempting Safe Data Migration...\n";
    
    try {
        // Check if income_logs table exists
        $checkIncomeLogsStmt = $db->prepare("SHOW TABLES LIKE 'income_logs'");
        $checkIncomeLogsStmt->execute();
        
        if ($checkIncomeLogsStmt->fetch()) {
            // Check if there's any data to migrate
            $countStmt = $db->query("SELECT COUNT(*) as count FROM income_logs WHERE matching_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)");
            $dataCount = $countStmt->fetch()['count'];
            
            if ($dataCount > 0) {
                echo "   Found {$dataCount} recent income records to migrate...\n";
                
                // Safe migration query
                $migrationSQL = "INSERT IGNORE INTO weekly_income_logs 
                               (user_id, week_start_date, week_end_date, left_pv, right_pv, matched_pv, 
                                gross_income, service_charge, tds_deduction, net_income, weekly_capping_applied, 
                                carry_forward_left, carry_forward_right, processing_status)
                               SELECT user_id, 
                                      matching_date as week_start_date,
                                      matching_date as week_end_date,
                                      COALESCE(left_pv, 0) as left_pv, 
                                      COALESCE(right_pv, 0) as right_pv, 
                                      COALESCE(matched_pv, 0) as matched_pv,
                                      COALESCE(income_amount, 0) as gross_income,
                                      0 as service_charge,
                                      0 as tds_deduction,
                                      COALESCE(income_amount, 0) as net_income,
                                      COALESCE(capping_applied, 0) as weekly_capping_applied,
                                      COALESCE(carry_forward_left, 0) as carry_forward_left, 
                                      COALESCE(carry_forward_right, 0) as carry_forward_right,
                                      'processed' as processing_status
                               FROM income_logs 
                               WHERE matching_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                               LIMIT 100";
                
                $db->exec($migrationSQL);
                echo "✅ Successfully migrated recent income data\n";
            } else {
                echo "ℹ️ No recent income data to migrate\n";
            }
        } else {
            echo "ℹ️ income_logs table not found - no data to migrate\n";
        }
    } catch (PDOException $e) {
        echo "⚠️ Data migration skipped due to error: " . $e->getMessage() . "\n";
        echo "   This is not critical - the system will work without migrated data\n";
    }
    echo "\n";
    
    // Step 5: Verification
    echo "5. Verifying System Setup...\n";
    
    $verificationQueries = [
        'PV Usage Tracking table' => "SELECT COUNT(*) as count FROM pv_usage_tracking",
        'Weekly Income Logs table' => "SELECT COUNT(*) as count FROM weekly_income_logs", 
        'Weekly Income Reports table' => "SELECT COUNT(*) as count FROM weekly_income_reports",
        'Enhanced config values' => "SELECT COUNT(*) as count FROM config WHERE config_key IN ('weekly_capping', 'service_charge_rate', 'tds_rate')"
    ];
    
    $allGood = true;
    foreach ($verificationQueries as $name => $query) {
        try {
            $stmt = $db->prepare($query);
            $stmt->execute();
            $result = $stmt->fetch();
            echo "✅ {$name}: " . ($result['count'] ?? 0) . " records\n";
        } catch (Exception $e) {
            echo "❌ {$name}: Verification failed\n";
            $allGood = false;
        }
    }
    echo "\n";
    
    // Summary
    echo str_repeat('=', 60) . "\n";
    echo "🎉 ENHANCED WEEKLY INCOME SYSTEM FIX COMPLETE!\n";
    echo str_repeat('=', 60) . "\n";
    
    if ($allGood) {
        echo "✅ All components verified successfully\n";
        echo "✅ Database tables created\n";
        echo "✅ Configuration values added\n";
        echo "✅ System ready for use\n\n";
        
        echo "📋 NEXT STEPS:\n";
        echo "1. Go to Admin Panel → Weekly Income Reports\n";
        echo "2. Click 'Generate Report' to test the system\n";
        echo "3. Reports should now appear properly\n";
        echo "4. Set up cron job: php setup_cron.php\n\n";
        
        echo "🌐 ADMIN PANEL ACCESS:\n";
        echo "URL: /admin/weekly-income-reports.php\n";
        echo "The enhanced deduction system is now fully operational!\n";
    } else {
        echo "⚠️ Some verification checks failed\n";
        echo "Please review the errors above and contact support if needed\n";
    }
    
} catch (Exception $e) {
    echo "❌ Fix failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
