<?php
/**
 * Fix Table Structure for Enhanced Weekly Income System
 * Updates existing tables to have the correct enhanced columns
 */

require_once 'config/database.php';
require_once 'config/Connection.php';

try {
    $db = Database::getInstance();
    
    echo "🔧 Fixing Table Structure for Enhanced Weekly Income System...\n\n";
    
    // Step 1: Check current table structures
    echo "1. Checking current table structures...\n";
    
    $tables = ['weekly_income_logs', 'weekly_income_reports'];
    
    foreach ($tables as $table) {
        echo "📋 Current structure of '{$table}':\n";
        try {
            $stmt = $db->query("DESCRIBE {$table}");
            $columns = $stmt->fetchAll();
            
            $columnNames = array_column($columns, 'Field');
            echo "   Columns: " . implode(', ', $columnNames) . "\n";
            
            // Check for missing enhanced columns
            if ($table === 'weekly_income_logs') {
                $requiredColumns = ['gross_income', 'service_charge', 'tds_deduction', 'net_income'];
                $missingColumns = array_diff($requiredColumns, $columnNames);
                
                if (!empty($missingColumns)) {
                    echo "   ❌ Missing columns: " . implode(', ', $missingColumns) . "\n";
                } else {
                    echo "   ✅ All enhanced columns present\n";
                }
            }
            
            if ($table === 'weekly_income_reports') {
                $requiredColumns = ['total_users_processed', 'total_gross_income', 'total_service_charges', 'total_tds_deductions', 'total_net_income'];
                $missingColumns = array_diff($requiredColumns, $columnNames);
                
                if (!empty($missingColumns)) {
                    echo "   ❌ Missing columns: " . implode(', ', $missingColumns) . "\n";
                } else {
                    echo "   ✅ All enhanced columns present\n";
                }
            }
            
        } catch (Exception $e) {
            echo "   ❌ Error checking table: " . $e->getMessage() . "\n";
        }
        echo "\n";
    }
    
    // Step 2: Fix weekly_income_logs table
    echo "2. Fixing weekly_income_logs table structure...\n";
    
    $weeklyLogsUpdates = [
        "ALTER TABLE weekly_income_logs ADD COLUMN gross_income DECIMAL(10,2) NOT NULL DEFAULT 0.00 AFTER matched_pv",
        "ALTER TABLE weekly_income_logs ADD COLUMN service_charge DECIMAL(10,2) NOT NULL DEFAULT 0.00 AFTER gross_income",
        "ALTER TABLE weekly_income_logs ADD COLUMN tds_deduction DECIMAL(10,2) NOT NULL DEFAULT 0.00 AFTER service_charge",
        "ALTER TABLE weekly_income_logs ADD COLUMN net_income DECIMAL(10,2) NOT NULL DEFAULT 0.00 AFTER tds_deduction",
        "ALTER TABLE weekly_income_logs ADD COLUMN processing_status ENUM('pending', 'processed', 'failed') DEFAULT 'pending' AFTER carry_forward_right",
        "ALTER TABLE weekly_income_logs ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at"
    ];
    
    foreach ($weeklyLogsUpdates as $update) {
        try {
            $db->exec($update);
            echo "   ✅ Applied: " . substr($update, 0, 50) . "...\n";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "   ℹ️ Column already exists: " . substr($update, 0, 50) . "...\n";
            } else {
                echo "   ❌ Error: " . $e->getMessage() . "\n";
            }
        }
    }
    echo "\n";
    
    // Step 3: Fix weekly_income_reports table
    echo "3. Fixing weekly_income_reports table structure...\n";
    
    $weeklyReportsUpdates = [
        "ALTER TABLE weekly_income_reports ADD COLUMN total_users_processed INT DEFAULT 0 AFTER week_end_date",
        "ALTER TABLE weekly_income_reports ADD COLUMN total_gross_income DECIMAL(12,2) DEFAULT 0.00 AFTER total_users_earned",
        "ALTER TABLE weekly_income_reports ADD COLUMN total_service_charges DECIMAL(12,2) DEFAULT 0.00 AFTER total_gross_income",
        "ALTER TABLE weekly_income_reports ADD COLUMN total_tds_deductions DECIMAL(12,2) DEFAULT 0.00 AFTER total_service_charges",
        "ALTER TABLE weekly_income_reports ADD COLUMN total_net_income DECIMAL(12,2) DEFAULT 0.00 AFTER total_tds_deductions",
        "ALTER TABLE weekly_income_reports ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at"
    ];
    
    foreach ($weeklyReportsUpdates as $update) {
        try {
            $db->exec($update);
            echo "   ✅ Applied: " . substr($update, 0, 50) . "...\n";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "   ℹ️ Column already exists: " . substr($update, 0, 50) . "...\n";
            } else {
                echo "   ❌ Error: " . $e->getMessage() . "\n";
            }
        }
    }
    echo "\n";
    
    // Step 4: Update existing data to populate new columns
    echo "4. Updating existing data...\n";
    
    try {
        // Update weekly_income_logs: set gross_income and net_income from income_amount if it exists
        $updateLogsSQL = "UPDATE weekly_income_logs SET 
                         gross_income = COALESCE(income_amount, 0),
                         service_charge = 0,
                         tds_deduction = 0,
                         net_income = COALESCE(income_amount, 0),
                         processing_status = 'processed'
                         WHERE gross_income = 0 AND income_amount IS NOT NULL";
        
        $db->exec($updateLogsSQL);
        echo "   ✅ Updated existing weekly_income_logs records\n";
        
    } catch (Exception $e) {
        echo "   ⚠️ Could not update existing data: " . $e->getMessage() . "\n";
    }
    
    try {
        // Update weekly_income_reports: set enhanced columns from existing data
        $updateReportsSQL = "UPDATE weekly_income_reports SET 
                            total_users_processed = COALESCE(total_users_earned, 0),
                            total_gross_income = COALESCE(total_income_distributed, 0),
                            total_service_charges = 0,
                            total_tds_deductions = 0,
                            total_net_income = COALESCE(total_income_distributed, 0)
                            WHERE total_gross_income = 0 AND total_income_distributed IS NOT NULL";
        
        $db->exec($updateReportsSQL);
        echo "   ✅ Updated existing weekly_income_reports records\n";
        
    } catch (Exception $e) {
        echo "   ⚠️ Could not update existing reports: " . $e->getMessage() . "\n";
    }
    echo "\n";
    
    // Step 5: Verify the fixes
    echo "5. Verifying table structures after fixes...\n";
    
    foreach ($tables as $table) {
        echo "📋 Updated structure of '{$table}':\n";
        try {
            $stmt = $db->query("DESCRIBE {$table}");
            $columns = $stmt->fetchAll();
            
            $columnNames = array_column($columns, 'Field');
            echo "   Columns: " . implode(', ', $columnNames) . "\n";
            
            // Verify enhanced columns
            if ($table === 'weekly_income_logs') {
                $requiredColumns = ['gross_income', 'service_charge', 'tds_deduction', 'net_income'];
                $missingColumns = array_diff($requiredColumns, $columnNames);
                
                if (empty($missingColumns)) {
                    echo "   ✅ All enhanced columns now present\n";
                } else {
                    echo "   ❌ Still missing: " . implode(', ', $missingColumns) . "\n";
                }
            }
            
            if ($table === 'weekly_income_reports') {
                $requiredColumns = ['total_users_processed', 'total_gross_income', 'total_service_charges', 'total_tds_deductions', 'total_net_income'];
                $missingColumns = array_diff($requiredColumns, $columnNames);
                
                if (empty($missingColumns)) {
                    echo "   ✅ All enhanced columns now present\n";
                } else {
                    echo "   ❌ Still missing: " . implode(', ', $missingColumns) . "\n";
                }
            }
            
        } catch (Exception $e) {
            echo "   ❌ Error verifying table: " . $e->getMessage() . "\n";
        }
        echo "\n";
    }
    
    // Step 6: Test the enhanced system
    echo "6. Testing enhanced system...\n";
    
    try {
        // Test inserting a sample record
        $testSQL = "INSERT IGNORE INTO weekly_income_logs 
                   (user_id, week_start_date, week_end_date, left_pv, right_pv, matched_pv, 
                    gross_income, service_charge, tds_deduction, net_income, processing_status) 
                   VALUES ('TEST_USER', '2025-01-01', '2025-01-07', 100, 100, 100, 10, 1, 0.5, 8.5, 'processed')";
        
        $db->exec($testSQL);
        echo "   ✅ Test insert successful\n";
        
        // Clean up test record
        $db->exec("DELETE FROM weekly_income_logs WHERE user_id = 'TEST_USER'");
        
    } catch (Exception $e) {
        echo "   ❌ Test insert failed: " . $e->getMessage() . "\n";
    }
    
    // Summary
    echo str_repeat('=', 60) . "\n";
    echo "🎉 TABLE STRUCTURE FIX COMPLETE!\n";
    echo str_repeat('=', 60) . "\n";
    
    echo "✅ Enhanced columns added to weekly_income_logs\n";
    echo "✅ Enhanced columns added to weekly_income_reports\n";
    echo "✅ Existing data updated with default values\n";
    echo "✅ Table structures verified\n\n";
    
    echo "📋 NEXT STEPS:\n";
    echo "1. Test the enhanced weekly matching:\n";
    echo "   php generate_test_weekly_income.php\n\n";
    echo "2. Or generate via admin panel:\n";
    echo "   Admin Panel → Weekly Income Reports → Generate Report\n\n";
    echo "3. Check results:\n";
    echo "   • Admin: /admin/weekly-income-reports.php\n";
    echo "   • User: /user/income-history.php\n\n";
    
    echo "✅ Enhanced weekly income system should now work properly!\n";
    
} catch (Exception $e) {
    echo "❌ Table structure fix failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
