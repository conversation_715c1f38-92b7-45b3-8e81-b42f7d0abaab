<?php
/**
 * Fix Admin Reports Issue
 * Diagnoses and fixes issues with weekly income reports not showing
 */

require_once 'config/database.php';
require_once 'config/Connection.php';

try {
    $db = Database::getInstance();
    
    echo "🔍 Diagnosing Admin Reports Issue...\n\n";
    
    // Check 1: Does weekly_income_reports table exist?
    echo "1. Checking if weekly_income_reports table exists...\n";
    $tableCheckStmt = $db->prepare("SHOW TABLES LIKE 'weekly_income_reports'");
    $tableCheckStmt->execute();
    $tableExists = $tableCheckStmt->fetch();
    
    if (!$tableExists) {
        echo "❌ Table 'weekly_income_reports' does not exist!\n";
        echo "🔧 Creating the table now...\n";
        
        $createTableSQL = "CREATE TABLE IF NOT EXISTS weekly_income_reports (
            id INT PRIMARY KEY AUTO_INCREMENT,
            week_start_date DATE NOT NULL,
            week_end_date DATE NOT NULL,
            total_users_processed INT DEFAULT 0,
            total_users_earned INT DEFAULT 0,
            total_gross_income DECIMAL(12,2) DEFAULT 0.00,
            total_service_charges DECIMAL(12,2) DEFAULT 0.00,
            total_tds_deductions DECIMAL(12,2) DEFAULT 0.00,
            total_net_income DECIMAL(12,2) DEFAULT 0.00,
            total_capping_applied DECIMAL(12,2) DEFAULT 0.00,
            report_status ENUM('processing', 'generated', 'sent', 'failed') DEFAULT 'processing',
            report_generated_at TIMESTAMP NULL,
            report_sent_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_week (week_start_date, week_end_date),
            INDEX idx_week_start_end (week_start_date, week_end_date),
            INDEX idx_report_status (report_status),
            INDEX idx_generated_at (report_generated_at)
        )";
        
        $db->exec($createTableSQL);
        echo "✅ Table 'weekly_income_reports' created successfully!\n";
    } else {
        echo "✅ Table 'weekly_income_reports' exists\n";
    }
    echo "\n";
    
    // Check 2: Does weekly_income_logs table exist?
    echo "2. Checking if weekly_income_logs table exists...\n";
    $logsTableCheckStmt = $db->prepare("SHOW TABLES LIKE 'weekly_income_logs'");
    $logsTableCheckStmt->execute();
    $logsTableExists = $logsTableCheckStmt->fetch();
    
    if (!$logsTableExists) {
        echo "❌ Table 'weekly_income_logs' does not exist!\n";
        echo "🔧 Creating the table now...\n";
        
        $createLogsTableSQL = "CREATE TABLE IF NOT EXISTS weekly_income_logs (
            id INT PRIMARY KEY AUTO_INCREMENT,
            user_id VARCHAR(20) NOT NULL,
            week_start_date DATE NOT NULL,
            week_end_date DATE NOT NULL,
            left_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            right_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            matched_pv DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            gross_income DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            service_charge DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            tds_deduction DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            net_income DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            weekly_capping_applied DECIMAL(10,2) DEFAULT 0.00,
            carry_forward_left DECIMAL(10,2) DEFAULT 0.00,
            carry_forward_right DECIMAL(10,2) DEFAULT 0.00,
            processing_status ENUM('pending', 'processed', 'failed') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            UNIQUE KEY unique_user_week (user_id, week_start_date),
            INDEX idx_week_start_date (week_start_date),
            INDEX idx_user_week (user_id, week_start_date),
            INDEX idx_net_income (net_income),
            INDEX idx_processing_status (processing_status)
        )";
        
        $db->exec($createLogsTableSQL);
        echo "✅ Table 'weekly_income_logs' created successfully!\n";
    } else {
        echo "✅ Table 'weekly_income_logs' exists\n";
    }
    echo "\n";
    
    // Check 3: Check for existing reports
    echo "3. Checking existing reports...\n";
    $reportsStmt = $db->query("SELECT COUNT(*) as count FROM weekly_income_reports");
    $reportCount = $reportsStmt->fetch()['count'];
    echo "📊 Found {$reportCount} existing reports\n";
    
    if ($reportCount > 0) {
        $recentReportsStmt = $db->query("SELECT * FROM weekly_income_reports ORDER BY week_start_date DESC LIMIT 3");
        $recentReports = $recentReportsStmt->fetchAll();
        
        echo "📋 Recent reports:\n";
        foreach ($recentReports as $report) {
            echo "   • Week {$report['week_start_date']} to {$report['week_end_date']}: ";
            echo "₹" . number_format($report['total_net_income'] ?? $report['total_income_distributed'] ?? 0, 2);
            echo " (Status: {$report['report_status']})\n";
        }
    } else {
        echo "ℹ️ No reports found - this is normal for a new system\n";
    }
    echo "\n";
    
    // Check 4: Check configuration values
    echo "4. Checking configuration values...\n";
    $configChecks = ['service_charge_rate', 'tds_rate', 'weekly_capping'];
    
    foreach ($configChecks as $configKey) {
        $configStmt = $db->prepare("SELECT config_value FROM config WHERE config_key = ?");
        $configStmt->execute([$configKey]);
        $configValue = $configStmt->fetch();
        
        if ($configValue) {
            echo "✅ {$configKey}: {$configValue['config_value']}\n";
        } else {
            echo "❌ {$configKey}: Missing\n";
            
            // Add missing config values
            $defaultValues = [
                'service_charge_rate' => '10.00',
                'tds_rate' => '5.00',
                'weekly_capping' => '130000.00'
            ];
            
            if (isset($defaultValues[$configKey])) {
                $insertConfigStmt = $db->prepare("INSERT INTO config (config_key, config_value, description) VALUES (?, ?, ?)");
                $descriptions = [
                    'service_charge_rate' => 'Service charge percentage (10%)',
                    'tds_rate' => 'TDS deduction percentage (5%)',
                    'weekly_capping' => 'Maximum weekly income per user in INR'
                ];
                
                $insertConfigStmt->execute([
                    $configKey, 
                    $defaultValues[$configKey], 
                    $descriptions[$configKey]
                ]);
                echo "🔧 Added {$configKey}: {$defaultValues[$configKey]}\n";
            }
        }
    }
    echo "\n";
    
    // Check 5: Test report generation for current week
    echo "5. Testing report generation...\n";
    
    require_once 'includes/PVSystem.php';
    $pvSystem = new PVSystem();
    
    $currentWeekStart = date('Y-m-d', strtotime('monday this week'));
    $currentWeekEnd = date('Y-m-d', strtotime('sunday this week'));
    
    echo "📅 Testing for week: {$currentWeekStart} to {$currentWeekEnd}\n";
    
    // Check if there are any active users
    $activeUsersStmt = $db->query("SELECT COUNT(*) as count FROM users WHERE status = 'active'");
    $activeUsersCount = $activeUsersStmt->fetch()['count'];
    echo "👥 Active users: {$activeUsersCount}\n";
    
    if ($activeUsersCount > 0) {
        echo "🧪 Running test report generation...\n";
        $result = $pvSystem->runWeeklyMatching($currentWeekStart, $currentWeekEnd);
        
        if ($result !== false) {
            echo "✅ Test report generation successful!\n";
            echo "   • Processed: {$result['processed']} users\n";
            echo "   • Users with income: {$result['users_with_income']}\n";
            echo "   • Total net income: ₹" . number_format($result['total_net_income'] ?? $result['total_income'] ?? 0, 2) . "\n";
        } else {
            echo "❌ Test report generation failed\n";
        }
    } else {
        echo "⚠️ No active users found - cannot test report generation\n";
    }
    echo "\n";
    
    // Summary
    echo str_repeat('=', 60) . "\n";
    echo "🎯 ADMIN REPORTS FIX SUMMARY\n";
    echo str_repeat('=', 60) . "\n";
    echo "✅ Database tables verified/created\n";
    echo "✅ Configuration values verified/added\n";
    echo "✅ Report generation tested\n";
    echo "\n";
    echo "📋 NEXT STEPS:\n";
    echo "1. Go to Admin Panel → Weekly Income Reports\n";
    echo "2. Click 'Generate Report' button\n";
    echo "3. Select week dates and generate\n";
    echo "4. Reports should now appear in the list\n";
    echo "\n";
    echo "🌐 ADMIN PANEL ACCESS:\n";
    echo "URL: /admin/weekly-income-reports.php\n";
    echo "The reports should now be visible after generation!\n";
    
} catch (Exception $e) {
    echo "❌ Fix failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
