<?php
/**
 * Cron Job Setup Script for Enhanced Weekly Matching Income System
 * Provides instructions and commands for setting up automated execution
 */

echo "🚀 Enhanced Weekly Matching Income System - Cron Setup\n";
echo str_repeat('=', 60) . "\n\n";

echo "📅 SCHEDULE REQUIREMENTS:\n";
echo "The enhanced weekly matching system should run every Saturday at 12:00 AM (midnight)\n";
echo "This ensures the full week (Monday-Sunday) is processed with all PV contributions.\n\n";

echo "⚙️ CRON JOB CONFIGURATION:\n";
echo "Add the following line to your crontab to run every Saturday at midnight:\n\n";

// Get the current script directory
$scriptPath = __DIR__ . '/cron/enhanced-weekly-matching.php';
echo "# Enhanced Weekly Matching Income (Every Saturday at 12:00 AM)\n";
echo "0 0 * * 6 /usr/bin/php {$scriptPath} >> /var/log/weekly-matching.log 2>&1\n\n";

echo "📝 CRON SCHEDULE EXPLANATION:\n";
echo "0 0 * * 6 breakdown:\n";
echo "├── 0     = Minute (0 = at the top of the hour)\n";
echo "├── 0     = Hour (0 = midnight)\n";
echo "├── *     = Day of month (any day)\n";
echo "├── *     = Month (any month)\n";
echo "└── 6     = Day of week (6 = Saturday)\n\n";

echo "🛠️ SETUP INSTRUCTIONS:\n";
echo "1. Open your crontab for editing:\n";
echo "   sudo crontab -e\n\n";

echo "2. Add the cron job line shown above\n\n";

echo "3. Save and exit the editor\n\n";

echo "4. Verify the cron job was added:\n";
echo "   sudo crontab -l\n\n";

echo "📊 SYSTEM FEATURES:\n";
echo "✅ Automated Saturday midnight execution\n";
echo "✅ 10% service charge deduction\n";
echo "✅ 5% TDS deduction\n";
echo "✅ PV usage tracking (prevents duplicate income)\n";
echo "✅ Enhanced reporting with deduction breakdown\n";
echo "✅ Admin email notifications\n";
echo "✅ Complete audit trail\n";
echo "✅ Weekly ₹130,000 income cap\n\n";

echo "🔍 MONITORING:\n";
echo "• Check logs: tail -f /var/log/weekly-matching.log\n";
echo "• View system logs in admin panel\n";
echo "• Monitor weekly income reports\n";
echo "• Review user income history for transparency\n\n";

echo "⚠️ IMPORTANT NOTES:\n";
echo "• Ensure PHP CLI is available at /usr/bin/php\n";
echo "• Verify database connectivity from cron environment\n";
echo "• Test the script manually before scheduling:\n";
echo "  php {$scriptPath}\n";
echo "• The script includes safety checks to prevent duplicate processing\n";
echo "• All deductions are clearly visible to users and admins\n\n";

echo "🧪 TESTING:\n";
echo "To test the enhanced weekly matching system:\n\n";
echo "1. Run manually for testing:\n";
echo "   php {$scriptPath}\n\n";
echo "2. Check the weekly income reports in admin panel\n\n";
echo "3. Verify deduction calculations:\n";
echo "   • Service charge should be exactly 10% of gross income\n";
echo "   • TDS should be exactly 5% of gross income\n";
echo "   • Net income = Gross - Service Charge - TDS\n\n";

echo "📧 EMAIL NOTIFICATIONS:\n";
echo "Admin will receive detailed email reports including:\n";
echo "• Total users processed\n";
echo "• Gross income generated\n";
echo "• Service charges collected\n";
echo "• TDS deductions applied\n";
echo "• Net income credited to users\n";
echo "• Capping applied (if any)\n\n";

echo "🔐 SECURITY & COMPLIANCE:\n";
echo "• All transactions are logged with timestamps\n";
echo "• Deductions are clearly itemized for transparency\n";
echo "• PV usage tracking prevents income manipulation\n";
echo "• Complete audit trail for regulatory compliance\n";
echo "• User dashboards show detailed income breakdown\n\n";

echo "✅ Setup complete! The enhanced weekly matching income system is ready.\n";
echo str_repeat('=', 60) . "\n";

// Check if we can write to log directory
$logDir = '/var/log';
if (!is_writable($logDir)) {
    echo "\n⚠️ WARNING: {$logDir} is not writable.\n";
    echo "Consider using an alternative log path or ensure proper permissions.\n";
    echo "Alternative: Create logs in project directory:\n";
    echo "0 0 * * 6 /usr/bin/php {$scriptPath} >> " . __DIR__ . "/logs/weekly-matching.log 2>&1\n\n";
}

// Check PHP CLI availability
$phpPath = '/usr/bin/php';
if (!file_exists($phpPath)) {
    echo "\n⚠️ WARNING: PHP CLI not found at {$phpPath}\n";
    echo "Find your PHP CLI path with: which php\n";
    echo "Then update the cron command accordingly.\n\n";
}

echo "For support or questions, refer to the system documentation.\n";
?>
