<?php
/**
 * User Income History with Enhanced Deduction Breakdown
 * Shows detailed weekly matching income with service charges and TDS
 */

session_start();

require_once '../includes/header.php';
require_once '../includes/Auth.php';
require_once '../includes/PVSystem.php';
require_once '../includes/Wallet.php';

// Require user authentication
Auth::requireUser();

$currentUser = Auth::user();
$userId = Auth::id();

// Initialize classes
$pvSystem = new PVSystem();
$wallet = new Wallet();

// Get database connection
$db = Database::getInstance();

// Get user's weekly income history - check which table exists and use appropriate query
$incomeHistory = [];

try {
    // First try the new enhanced weekly_income_logs table
    $checkNewTableStmt = $db->prepare("SHOW TABLES LIKE 'weekly_income_logs'");
    $checkNewTableStmt->execute();

    if ($checkNewTableStmt->fetch()) {
        // New enhanced table exists
        $incomeHistoryStmt = $db->prepare("
            SELECT
                wil.*,
                DATE_FORMAT(wil.week_start_date, '%M %d') as week_start_formatted,
                DATE_FORMAT(wil.week_end_date, '%M %d, %Y') as week_end_formatted
            FROM weekly_income_logs wil
            WHERE wil.user_id = ?
            ORDER BY wil.week_start_date DESC
            LIMIT 20
        ");
        $incomeHistoryStmt->execute([$userId]);
        $incomeHistory = $incomeHistoryStmt->fetchAll();
    } else {
        // Fall back to old income_logs table
        $checkOldTableStmt = $db->prepare("SHOW TABLES LIKE 'income_logs'");
        $checkOldTableStmt->execute();

        if ($checkOldTableStmt->fetch()) {
            $incomeHistoryStmt = $db->prepare("
                SELECT
                    il.*,
                    il.income_amount as gross_income,
                    0 as service_charge,
                    0 as tds_deduction,
                    il.income_amount as net_income,
                    il.capping_applied as weekly_capping_applied,
                    'processed' as processing_status,
                    il.matching_date as week_start_date,
                    il.matching_date as week_end_date,
                    DATE_FORMAT(il.matching_date, '%M %d') as week_start_formatted,
                    DATE_FORMAT(il.matching_date, '%M %d, %Y') as week_end_formatted
                FROM income_logs il
                WHERE il.user_id = ?
                ORDER BY il.matching_date DESC
                LIMIT 20
            ");
            $incomeHistoryStmt->execute([$userId]);
            $incomeHistory = $incomeHistoryStmt->fetchAll();
        }
    }
} catch (Exception $e) {
    error_log("Income history query error: " . $e->getMessage());
    $incomeHistory = [];
}

// Calculate totals with fallbacks for compatibility
$totalGrossIncome = 0;
$totalServiceCharges = 0;
$totalTdsDeductions = 0;
$totalNetIncome = 0;
$totalCapping = 0;

foreach ($incomeHistory as $income) {
    $grossIncome = $income['gross_income'] ?? $income['income_amount'] ?? 0;
    $serviceCharge = $income['service_charge'] ?? 0;
    $tdsDeduction = $income['tds_deduction'] ?? 0;
    $netIncome = $income['net_income'] ?? $income['income_amount'] ?? 0;
    $capping = $income['weekly_capping_applied'] ?? $income['capping_applied'] ?? 0;

    $totalGrossIncome += $grossIncome;
    $totalServiceCharges += $serviceCharge;
    $totalTdsDeductions += $tdsDeduction;
    $totalNetIncome += $netIncome;
    $totalCapping += $capping;
}

// Get wallet transaction history for deductions
$deductionHistoryStmt = $db->prepare("
    SELECT 
        wt.*,
        DATE_FORMAT(wt.created_at, '%M %d, %Y %H:%i') as formatted_date
    FROM wallet_transactions wt
    WHERE wt.user_id = ? 
    AND wt.reference_type IN ('service_charge', 'tds', 'weekly_matching')
    ORDER BY wt.created_at DESC
    LIMIT 50
");
$deductionHistoryStmt->execute([$userId]);
$deductionHistory = $deductionHistoryStmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Income History - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-network-wired me-2"></i><?php echo SITE_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="income-history.php">
                            <i class="fas fa-chart-line me-1"></i>Income History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="tree.php">
                            <i class="fas fa-sitemap me-1"></i>Binary Tree
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="wallet.php">
                            <i class="fas fa-wallet me-1"></i>Wallet
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($currentUser['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>Profile</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2>
                            <i class="fas fa-chart-line me-2"></i>
                            Income History & Breakdown
                        </h2>
                        <p class="text-muted mb-0">Detailed view of your weekly matching income with transparent deduction breakdown</p>
                    </div>
                    <a href="dashboard.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h6 class="card-title">Total Weeks</h6>
                        <h3><?php echo count($incomeHistory); ?></h3>
                        <small>with income</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h6 class="card-title">Gross Income</h6>
                        <h4>₹<?php echo number_format($totalGrossIncome, 0); ?></h4>
                        <small>before deductions</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h6 class="card-title">Service Charges</h6>
                        <h4>₹<?php echo number_format($totalServiceCharges, 0); ?></h4>
                        <small>10% deducted</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-secondary text-white">
                    <div class="card-body text-center">
                        <h6 class="card-title">TDS</h6>
                        <h4>₹<?php echo number_format($totalTdsDeductions, 0); ?></h4>
                        <small>5% deducted</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h6 class="card-title">Net Income</h6>
                        <h4>₹<?php echo number_format($totalNetIncome, 0); ?></h4>
                        <small>credited</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h6 class="card-title">Capping</h6>
                        <h4>₹<?php echo number_format($totalCapping, 0); ?></h4>
                        <small>applied</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transparency Information -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h5 class="alert-heading">
                        <i class="fas fa-info-circle me-2"></i>
                        Income Transparency & Deduction Details
                    </h5>
                    <p class="mb-2">Your weekly matching income is calculated transparently with the following deductions:</p>
                    <ul class="mb-0">
                        <li><strong>Service Charge (10%):</strong> Administrative fee for system maintenance and support</li>
                        <li><strong>TDS (5%):</strong> Tax Deducted at Source as per regulatory requirements</li>
                        <li><strong>Net Income:</strong> The amount credited to your wallet after deductions</li>
                        <li><strong>Weekly Cap:</strong> Maximum ₹130,000 gross income per week to ensure fair distribution</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Weekly Income History -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-history me-2"></i>
                            Weekly Income History
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($incomeHistory)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No Income History Yet</h5>
                                <p class="text-muted">Your weekly matching income will appear here once you start earning.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Week Period</th>
                                            <th>PV Details</th>
                                            <th>Gross Income</th>
                                            <th>Service Charge</th>
                                            <th>TDS</th>
                                            <th>Net Income</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($incomeHistory as $income): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo $income['week_start_formatted']; ?> - <?php echo $income['week_end_formatted']; ?></strong>
                                                </td>
                                                <td>
                                                    <div class="small">
                                                        <div><strong>Left:</strong> <span class="badge bg-info"><?php echo number_format($income['left_pv'], 0); ?></span></div>
                                                        <div><strong>Right:</strong> <span class="badge bg-info"><?php echo number_format($income['right_pv'], 0); ?></span></div>
                                                        <div><strong>Matched:</strong> <span class="badge bg-success"><?php echo number_format($income['matched_pv'], 0); ?></span></div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php
                                                    $grossIncome = $income['gross_income'] ?? $income['income_amount'] ?? 0;
                                                    ?>
                                                    <strong class="text-primary">₹<?php echo number_format($grossIncome, 2); ?></strong>
                                                </td>
                                                <td>
                                                    <?php
                                                    $serviceCharge = $income['service_charge'] ?? 0;
                                                    ?>
                                                    <span class="text-warning">₹<?php echo number_format($serviceCharge, 2); ?></span>
                                                    <small class="text-muted d-block">(10%)</small>
                                                </td>
                                                <td>
                                                    <?php
                                                    $tdsDeduction = $income['tds_deduction'] ?? 0;
                                                    ?>
                                                    <span class="text-info">₹<?php echo number_format($tdsDeduction, 2); ?></span>
                                                    <small class="text-muted d-block">(5%)</small>
                                                </td>
                                                <td>
                                                    <?php
                                                    $netIncome = $income['net_income'] ?? $income['income_amount'] ?? 0;
                                                    ?>
                                                    <strong class="text-success">₹<?php echo number_format($netIncome, 2); ?></strong>
                                                    <small class="text-muted d-block">credited</small>
                                                </td>
                                                <td>
                                                    <?php
                                                    $statusClass = [
                                                        'pending' => 'bg-warning',
                                                        'processed' => 'bg-success',
                                                        'failed' => 'bg-danger'
                                                    ];
                                                    $processingStatus = $income['processing_status'] ?? 'processed';
                                                    $cappingApplied = $income['weekly_capping_applied'] ?? $income['capping_applied'] ?? 0;
                                                    ?>
                                                    <span class="badge <?php echo $statusClass[$processingStatus] ?? 'bg-secondary'; ?>">
                                                        <?php echo ucfirst($processingStatus); ?>
                                                    </span>
                                                    <?php if ($cappingApplied > 0): ?>
                                                        <br><small class="text-warning">Capped: ₹<?php echo number_format($cappingApplied, 2); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
