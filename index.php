<?php
/**
 * Main Index Page
 * MLM Binary Plan System
 */

// Enable error reporting in development
if (!defined('ENVIRONMENT') || ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// Include essential files
require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/session.php';
require_once 'includes/functions.php';
require_once 'includes/FileUpload.php';

// Check if user is already logged in and redirect accordingly
if (isLoggedIn()) {
    $userType = getCurrentUserType();
    switch ($userType) {
        case 'admin':
            header("Location: admin/dashboard.php");
            break;
        case 'franchise':
            header("Location: franchise/dashboard.php");
            break;
        case 'user':
            header("Location: user/dashboard.php");
            break;
        default:
            destroyUserSession();
            break;
    }
    exit();
}

// Get featured products for homepage
$db = Database::getInstance();
$productsStmt = $db->prepare("SELECT * FROM products WHERE status = 'active' ORDER BY created_at DESC LIMIT 6");
$productsStmt->execute();
$featuredProducts = $productsStmt->fetchAll();

$fileUpload = new FileUpload();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> - MLM Binary Plan System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
        }
        .feature-card {
            transition: transform 0.3s;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .login-card {
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-network-wired me-2"></i><?php echo SITE_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">Features</a>
                    </li>
                      <li class="nav-item">
                        <a class="nav-link" href="products.php">Product</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">Contact</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section text-center">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <h1 class="display-4 fw-bold mb-4">Welcome to <?php echo SITE_NAME; ?></h1>
                    <p class="lead mb-5">Experience the power of Binary MLM system with advanced PV matching and unlimited earning potential.</p>
                    <div class="row justify-content-center">
                        <div class="col-md-4 mb-3">
                            <div class="card login-card">
                                <div class="card-body text-center text-dark">
                                    <i class="fas fa-user-shield fa-3x text-primary mb-3"></i>
                                    <h5>Admin Login</h5>
                                    <p class="text-muted">System administration and management</p>
                                    <a href="admin/login.php" class="btn btn-primary">Login as Admin</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card login-card">
                                <div class="card-body text-center text-dark">
                                    <i class="fas fa-store fa-3x text-success mb-3"></i>
                                    <h5>Franchise Login</h5>
                                    <p class="text-muted">Manage your franchise operations</p>
                                    <a href="franchise/login.php" class="btn btn-success">Login as Franchise</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card login-card">
                                <div class="card-body text-center text-dark">
                                    <i class="fas fa-users fa-3x text-info mb-3"></i>
                                    <h5>User Login</h5>
                                    <p class="text-muted">Access your MLM dashboard</p>
                                    <a href="user/login.php" class="btn btn-info">Login as User</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5">
                    <h2 class="fw-bold">System Features</h2>
                    <p class="text-muted">Powerful MLM features designed for success</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-sitemap fa-3x text-primary mb-3"></i>
                            <h5>Binary Tree System</h5>
                            <p class="text-muted">Advanced binary tree structure with left/right placement and visual representation.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-coins fa-3x text-success mb-3"></i>
                            <h5>PV Matching System</h5>
                            <p class="text-muted">Intelligent PV matching with carry forward and daily capping features.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-wallet fa-3x text-info mb-3"></i>
                            <h5>Digital Wallet</h5>
                            <p class="text-muted">Secure digital wallet with instant income credit and withdrawal options.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <?php if (!empty($featuredProducts)): ?>
    <section id="products" class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5">
                    <h2 class="fw-bold">Our Products</h2>
                    <p class="text-muted">Discover our premium product range</p>
                </div>
            </div>
            <div class="row">
                <?php foreach ($featuredProducts as $product): ?>
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card product-card h-100 shadow-sm">
                            <div class="card-img-container" style="height: 200px; overflow: hidden;">
                                <?php if ($product['image']): ?>
                                    <img src="<?php echo htmlspecialchars($fileUpload->getFileUrl($product['image'])); ?>"
                                         class="card-img-top"
                                         alt="<?php echo htmlspecialchars($product['name']); ?>"
                                         style="width: 100%; height: 100%; object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-light d-flex align-items-center justify-content-center h-100">
                                        <i class="fas fa-image fa-3x text-muted"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                                <p class="card-text text-muted flex-grow-1">
                                    <?php echo htmlspecialchars(substr($product['description'], 0, 100)); ?>
                                    <?php echo strlen($product['description']) > 100 ? '...' : ''; ?>
                                </p>
                                <div class="row mb-3">
                                    <div class="col-6">
                                        <strong class="text-success">₹<?php echo number_format($product['price'], 2); ?></strong>
                                    </div>
                                    <div class="col-6 text-end">
                                        <span class="badge bg-primary"><?php echo formatPV($product['pv_value']); ?> PV</span>
                                    </div>
                                </div>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="viewProduct(<?php echo $product['id']; ?>)">
                                        <i class="fas fa-eye me-2"></i>View Details
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <div class="row">
                <div class="col-12 text-center mt-4">
                    <a href="products.php" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-shopping-cart me-2"></i>View All Products
                    </a>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; 2024 <?php echo SITE_NAME; ?>. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-end">
                    <p>Powered by Advanced MLM Technology</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <style>
        .product-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        }

        .card-img-container {
            position: relative;
            overflow: hidden;
        }

        .card-img-top {
            transition: transform 0.3s ease;
        }

        .product-card:hover .card-img-top {
            transform: scale(1.05);
        }
    </style>

    <script>
        function viewProduct(productId) {
            // Redirect to product detail page
            window.location.href = 'product-detail.php?id=' + productId;
        }
    </script>
</body>
</html>
