<?php
/**
 * Generate Test Weekly Income
 * Manually generates weekly income for testing purposes
 */

require_once 'config/database.php';
require_once 'config/Connection.php';
require_once 'includes/PVSystem.php';

try {
    $db = Database::getInstance();
    $pvSystem = new PVSystem();
    
    echo "🚀 Generating Test Weekly Income...\n\n";
    
    // Get current week dates
    $currentWeekStart = date('Y-m-d', strtotime('monday this week'));
    $currentWeekEnd = date('Y-m-d', strtotime('sunday this week'));
    
    echo "📅 Processing week: {$currentWeekStart} to {$currentWeekEnd}\n\n";
    
    // Check if already processed
    $checkStmt = $db->prepare("SELECT COUNT(*) as count FROM weekly_income_reports WHERE week_start_date = ?");
    $checkStmt->execute([$currentWeekStart]);
    $alreadyProcessed = $checkStmt->fetch()['count'] > 0;
    
    if ($alreadyProcessed) {
        echo "⚠️ Current week already processed. Trying previous week...\n";
        $currentWeekStart = date('Y-m-d', strtotime('monday last week'));
        $currentWeekEnd = date('Y-m-d', strtotime('sunday last week'));
        echo "📅 Processing week: {$currentWeekStart} to {$currentWeekEnd}\n\n";
        
        $checkStmt->execute([$currentWeekStart]);
        $alreadyProcessed = $checkStmt->fetch()['count'] > 0;
        
        if ($alreadyProcessed) {
            echo "⚠️ Previous week also processed. Using custom test week...\n";
            $currentWeekStart = date('Y-m-d', strtotime('-14 days'));
            $currentWeekEnd = date('Y-m-d', strtotime('-8 days'));
            echo "📅 Processing week: {$currentWeekStart} to {$currentWeekEnd}\n\n";
        }
    }
    
    // Check if we have PV data
    $pvCountStmt = $db->query("SELECT COUNT(*) as count FROM pv_transactions");
    $pvCount = $pvCountStmt->fetch()['count'];
    
    if ($pvCount == 0) {
        echo "❌ No PV transactions found!\n";
        echo "🔧 Run this first: php create_test_data.php\n";
        exit(1);
    }
    
    echo "✅ Found {$pvCount} PV transactions\n\n";
    
    // Show current PV status
    echo "📊 Current PV Status:\n";
    $pvStatusStmt = $db->query("
        SELECT user_id, 
               SUM(CASE WHEN side = 'left' THEN pv_amount ELSE 0 END) as left_pv,
               SUM(CASE WHEN side = 'right' THEN pv_amount ELSE 0 END) as right_pv
        FROM pv_transactions 
        GROUP BY user_id 
        HAVING left_pv > 0 OR right_pv > 0
        ORDER BY user_id
        LIMIT 10
    ");
    $pvStatus = $pvStatusStmt->fetchAll();
    
    foreach ($pvStatus as $user) {
        $matchable = min($user['left_pv'], $user['right_pv']);
        echo "   • {$user['user_id']}: Left {$user['left_pv']}, Right {$user['right_pv']}, Matchable: {$matchable}\n";
    }
    echo "\n";
    
    // Run weekly matching
    echo "🔄 Running weekly matching process...\n";
    
    $result = $pvSystem->runWeeklyMatching($currentWeekStart, $currentWeekEnd);
    
    if ($result !== false) {
        echo "✅ Weekly matching completed successfully!\n\n";
        
        echo "📋 RESULTS:\n";
        echo "   • Users processed: " . ($result['processed'] ?? 0) . "\n";
        echo "   • Users with income: " . ($result['users_with_income'] ?? 0) . "\n";
        echo "   • Total gross income: ₹" . number_format($result['total_gross_income'] ?? 0, 2) . "\n";
        echo "   • Total service charges: ₹" . number_format($result['total_service_charges'] ?? 0, 2) . "\n";
        echo "   • Total TDS deductions: ₹" . number_format($result['total_tds_deductions'] ?? 0, 2) . "\n";
        echo "   • Total net income: ₹" . number_format($result['total_net_income'] ?? $result['total_income'] ?? 0, 2) . "\n";
        echo "   • Total capping applied: ₹" . number_format($result['total_capping'] ?? 0, 2) . "\n\n";
        
        // Verify data was created
        echo "🔍 Verifying created data...\n";
        
        // Check weekly_income_logs
        $logsStmt = $db->prepare("SELECT COUNT(*) as count FROM weekly_income_logs WHERE week_start_date = ?");
        $logsStmt->execute([$currentWeekStart]);
        $logsCount = $logsStmt->fetch()['count'];
        echo "   • Weekly income logs created: {$logsCount}\n";
        
        // Check weekly_income_reports
        $reportsStmt = $db->prepare("SELECT COUNT(*) as count FROM weekly_income_reports WHERE week_start_date = ?");
        $reportsStmt->execute([$currentWeekStart]);
        $reportsCount = $reportsStmt->fetch()['count'];
        echo "   • Weekly income reports created: {$reportsCount}\n";
        
        // Show sample records
        if ($logsCount > 0) {
            echo "\n📋 Sample income records:\n";
            $sampleStmt = $db->prepare("
                SELECT user_id, gross_income, service_charge, tds_deduction, net_income 
                FROM weekly_income_logs 
                WHERE week_start_date = ? AND net_income > 0
                ORDER BY net_income DESC 
                LIMIT 5
            ");
            $sampleStmt->execute([$currentWeekStart]);
            $samples = $sampleStmt->fetchAll();
            
            foreach ($samples as $sample) {
                echo "   • {$sample['user_id']}: Gross ₹{$sample['gross_income']}, Net ₹{$sample['net_income']}\n";
            }
        }
        
        echo "\n";
        
    } else {
        echo "❌ Weekly matching failed!\n";
        echo "Check the error logs for details.\n";
        exit(1);
    }
    
    // Final verification
    echo str_repeat('=', 60) . "\n";
    echo "🎉 WEEKLY INCOME GENERATION COMPLETE!\n";
    echo str_repeat('=', 60) . "\n";
    
    echo "✅ Weekly income data has been generated\n";
    echo "✅ Users should now see income history\n";
    echo "✅ Admin should see reports\n\n";
    
    echo "🌐 CHECK THESE PAGES:\n";
    echo "   • Admin: /admin/weekly-income-reports.php\n";
    echo "   • Admin: /admin/weekly-income-details.php?week={$currentWeekStart}\n";
    echo "   • User: /user/dashboard.php (Recent Income section)\n";
    echo "   • User: /user/income-history.php\n\n";
    
    echo "🧪 TEST USERS (if created):\n";
    echo "   • TEST001, TEST002, TEST003, TEST004, TEST005\n";
    echo "   • Password: password123\n\n";
    
    echo "✅ Weekly income system is now working with test data!\n";
    
} catch (Exception $e) {
    echo "❌ Weekly income generation failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
